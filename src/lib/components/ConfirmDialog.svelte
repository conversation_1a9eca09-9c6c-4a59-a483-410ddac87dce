<script lang="ts">
	import { createEventDispatcher } from 'svelte';

	export let isOpen = false;
	export let title = 'Potvrzení';
	export let message = 'Opravdu chcete pokračovat?';
	export let confirmText = 'Ano';
	export let cancelText = 'Ne';
	export let confirmButtonClass = 'bg-red-600 hover:bg-red-700 text-white';

	const dispatch = createEventDispatcher<{
		confirm: void;
		cancel: void;
	}>();

	function handleConfirm() {
		dispatch('confirm');
		isOpen = false;
	}

	function handleCancel() {
		dispatch('cancel');
		isOpen = false;
	}

	function handleBackdropClick(event: MouseEvent) {
		if (event.target === event.currentTarget) {
			handleCancel();
		}
	}
</script>

{#if isOpen}
	<!-- Modal backdrop -->
	<div 
		class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
		onclick={handleBackdropClick}
		role="dialog"
		aria-modal="true"
		aria-labelledby="dialog-title"
	>
		<!-- Modal content -->
		<div class="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/20 p-6 w-full max-w-md animate-in slide-in-from-bottom-5 duration-300">
			<!-- Header -->
			<div class="flex items-center space-x-3 mb-4">
				<div class="w-10 h-10 bg-gradient-to-br from-red-500 to-orange-600 rounded-xl flex items-center justify-center">
					<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
					</svg>
				</div>
				<h2 id="dialog-title" class="text-xl font-bold text-gray-900">
					{title}
				</h2>
			</div>

			<!-- Message -->
			<p class="text-gray-700 mb-6 leading-relaxed">
				{message}
			</p>

			<!-- Buttons -->
			<div class="flex flex-col sm:flex-row gap-3 sm:justify-end">
				<button
					onclick={handleCancel}
					class="px-6 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium"
				>
					{cancelText}
				</button>
				<button
					onclick={handleConfirm}
					class="px-6 py-3 rounded-xl font-medium transition-all duration-200 {confirmButtonClass}"
				>
					{confirmText}
				</button>
			</div>
		</div>
	</div>
{/if}

<style>
	@keyframes slide-in-from-bottom {
		from {
			opacity: 0;
			transform: translateY(20px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.slide-in-from-bottom-5 {
		animation-name: slide-in-from-bottom;
	}
</style>
