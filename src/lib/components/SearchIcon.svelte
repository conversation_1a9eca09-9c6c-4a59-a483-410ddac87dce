<script lang="ts">
	import { colorSchemes, type ColorScheme } from '$lib/stores/theme';

	export let onclick: (() => void) | undefined = undefined;
	export let colorScheme: ColorScheme = 'blue';

	$: currentColors = colorSchemes[colorScheme];
</script>

<!-- Search/Filter Icon -->
<button
	onclick={onclick}
	class="group w-12 h-12 bg-gradient-to-br {currentColors.colors.accent} rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 flex items-center justify-center border border-white/20 backdrop-blur-sm"
	aria-label="Otev<PERSON><PERSON><PERSON> filtry"
>
	<!-- Search icon -->
	<svg
		class="w-6 h-6 text-white group-hover:scale-110 transition-transform duration-200"
		fill="none"
		stroke="currentColor"
		viewBox="0 0 24 24"
	>
		<path
			stroke-linecap="round"
			stroke-linejoin="round"
			stroke-width="2.5"
			d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
		/>
	</svg>
</button>
