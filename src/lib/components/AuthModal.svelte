<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { supabase } from '$lib/supabase';

	export let isOpen = false;

	const dispatch = createEventDispatcher<{
		close: void;
		success: void;
	}>();

	let email = '';
	let password = '';
	let isSignUp = false;
	let loading = false;
	let error = '';

	async function handleSubmit() {
		if (!email || !password) {
			error = 'Vyplňte všechna pole';
			return;
		}

		loading = true;
		error = '';

		try {
			if (isSignUp) {
				const { error: signUpError } = await supabase.auth.signUp({
					email,
					password
				});
				if (signUpError) throw signUpError;
				error = 'Zkontrolujte email pro potvrzení registrace';
			} else {
				const { error: signInError } = await supabase.auth.signInWithPassword({
					email,
					password
				});
				if (signInError) throw signInError;
				dispatch('success');
				dispatch('close');
			}
		} catch (err) {
			console.error('Auth error:', err);
			error = err instanceof Error ? err.message : 'Chyba při přihlášení';
		} finally {
			loading = false;
		}
	}

	function closeModal() {
		isOpen = false;
		dispatch('close');
	}
</script>

{#if isOpen}
	<!-- Modal backdrop -->
	<div class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
		<!-- Modal content -->
		<div class="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/20 p-8 w-full max-w-md">
			<!-- Header -->
			<div class="flex items-center justify-between mb-6">
				<h2 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
					{isSignUp ? 'Registrace' : 'Přihlášení'}
				</h2>
				<button 
					on:click={closeModal}
					class="text-gray-400 hover:text-gray-600 p-1"
					aria-label="Zavřít"
				>
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>

			<!-- Error message -->
			{#if error}
				<div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
					<p class="text-sm text-red-700">{error}</p>
				</div>
			{/if}

			<!-- Form -->
			<form on:submit|preventDefault={handleSubmit} class="space-y-4">
				<div>
					<label for="email" class="block text-sm font-medium text-gray-700 mb-2">
						Email
					</label>
					<input
						id="email"
						type="email"
						bind:value={email}
						required
						class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
						placeholder="<EMAIL>"
					/>
				</div>

				<div>
					<label for="password" class="block text-sm font-medium text-gray-700 mb-2">
						Heslo
					</label>
					<input
						id="password"
						type="password"
						bind:value={password}
						required
						minlength="6"
						class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
						placeholder="Minimálně 6 znaků"
					/>
				</div>

				<button
					type="submit"
					disabled={loading}
					class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
				>
					{#if loading}
						<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
						<span>Načítám...</span>
					{:else}
						<span>{isSignUp ? 'Registrovat' : 'Přihlásit'}</span>
					{/if}
				</button>
			</form>

			<!-- Toggle mode -->
			<div class="mt-6 text-center">
				<button
					on:click={() => {
						isSignUp = !isSignUp;
						error = '';
					}}
					class="text-sm text-blue-600 hover:text-blue-800 transition-colors"
				>
					{isSignUp ? 'Už máte účet? Přihlaste se' : 'Nemáte účet? Registrujte se'}
				</button>
			</div>

			<!-- Info -->
			<div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
				<p class="text-xs text-blue-800">
					💡 <strong>Tip:</strong> Pro přidávání míst je potřeba být přihlášen. 
					Registrace je zdarma a trvá jen chvilku.
				</p>
			</div>
		</div>
	</div>
{/if}
