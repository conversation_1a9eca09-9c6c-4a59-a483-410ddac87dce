import { describe, it, expect } from 'vitest';
import { render, fireEvent } from '@testing-library/svelte';
import FilterPopup from './FilterPopup.svelte';

describe('FilterPopup', () => {
	it('should render when isOpen is true', () => {
		const { getByText } = render(FilterPopup, {
			props: {
				isOpen: true,
				selectedCategories: [],
				onfilterchange: () => {},
				onclose: () => {}
			}
		});

		expect(getByText('Filtry kategorií')).toBeInTheDocument();
	});

	it('should not render when isOpen is false', () => {
		const { queryByText } = render(FilterPopup, {
			props: {
				isOpen: false,
				selectedCategories: [],
				onfilterchange: () => {},
				onclose: () => {}
			}
		});

		expect(queryByText('Filtry kategorií')).not.toBeInTheDocument();
	});

	it('should show all categories', () => {
		const { getByText } = render(FilterPopup, {
			props: {
				isOpen: true,
				selectedCategories: [],
				onfilterchange: () => {},
				onclose: () => {}
			}
		});

		expect(getByText('Veřejné toalety')).toBeInTheDocument();
		expect(getByText('WiFi zdarma')).toBeInTheDocument();
		expect(getByText('Místa na běhání')).toBeInTheDocument();
		expect(getByText('Parky')).toBeInTheDocument();
		expect(getByText('Obchody/Večerky')).toBeInTheDocument();
		expect(getByText('Kavárny')).toBeInTheDocument();
		expect(getByText('Restaurace/Bary')).toBeInTheDocument();
	});

	it('should call onfilterchange when category is toggled', async () => {
		let filterChangeCalled = false;
		let receivedCategories: string[] = [];

		const { getByLabelText } = render(FilterPopup, {
			props: {
				isOpen: true,
				selectedCategories: [],
				onfilterchange: (categories: string[]) => {
					filterChangeCalled = true;
					receivedCategories = categories;
				},
				onclose: () => {}
			}
		});

		const toiletCheckbox = getByLabelText('Veřejné toalety');
		await fireEvent.click(toiletCheckbox);

		expect(filterChangeCalled).toBe(true);
		expect(receivedCategories).toContain('toilet');
	});

	it('should call onclose when close button is clicked', async () => {
		let closeCalled = false;

		const { getByLabelText } = render(FilterPopup, {
			props: {
				isOpen: true,
				selectedCategories: [],
				onfilterchange: () => {},
				onclose: () => {
					closeCalled = true;
				}
			}
		});

		const closeButton = getByLabelText('Zavřít');
		await fireEvent.click(closeButton);

		expect(closeCalled).toBe(true);
	});
});
