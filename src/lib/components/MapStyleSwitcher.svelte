<script lang="ts">
	export let currentStyle: string = 'auto';
	export let onstylechange: ((event: { style: string }) => void) | undefined = undefined;

	const mapStyles = [
		{
			id: 'auto',
			name: '<PERSON><PERSON>',
			icon: '🌅',
			description: 'Podle času dne',
			style: 'auto'
		},
		{
			id: 'day',
			name: '<PERSON>',
			icon: '☀️',
			description: '<PERSON><PERSON><PERSON><PERSON><PERSON> re<PERSON>',
			style: 'mapbox://styles/mapbox/streets-v12'
		},
		{
			id: 'night',
			name: '<PERSON><PERSON>',
			icon: '🌙',
			description: '<PERSON><PERSON><PERSON><PERSON> režim',
			style: 'mapbox://styles/mapbox/dark-v11'
		},
		{
			id: 'dawn',
			name: '<PERSON>svit',
			icon: '🌄',
			description: 'Je<PERSON><PERSON><PERSON> barvy',
			style: 'mapbox://styles/mapbox/light-v11'
		},
		{
			id: 'dusk',
			name: '<PERSON>um<PERSON>',
			icon: '🌆',
			description: 'Teplé tóny',
			style: 'mapbox://styles/mapbox/satellite-streets-v12'
		}
	];

	function handleStyleChange(styleId: string) {
		currentStyle = styleId;
		const selectedStyle = mapStyles.find(s => s.id === styleId);
		if (selectedStyle && onstylechange) {
			onstylechange({ style: selectedStyle.style });
		}
	}

	// Get current time-based style
	export function getAutoStyle(): string {
		const hour = new Date().getHours();

		if (hour >= 6 && hour < 10) {
			return 'mapbox://styles/mapbox/light-v11'; // Dawn
		} else if (hour >= 10 && hour < 18) {
			return 'mapbox://styles/mapbox/streets-v12'; // Day
		} else if (hour >= 18 && hour < 21) {
			return 'mapbox://styles/mapbox/satellite-streets-v12'; // Dusk
		} else {
			return 'mapbox://styles/mapbox/dark-v11'; // Night
		}
	}
</script>

<!-- Style switcher panel -->
<div class="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/20 p-4">
	<div class="flex items-center space-x-2 mb-3">
		<div class="w-6 h-6 bg-gradient-to-br from-orange-500 to-pink-600 rounded-lg flex items-center justify-center">
			<span class="text-white text-xs">🎨</span>
		</div>
		<h3 class="font-semibold text-gray-900 text-sm">Styl mapy</h3>
	</div>

	<div class="grid grid-cols-2 gap-2">
		{#each mapStyles as style}
			<button
				onclick={() => handleStyleChange(style.id)}
				class="group p-3 rounded-xl border-2 transition-all duration-200 text-left {
					currentStyle === style.id
						? 'border-blue-500 bg-blue-50'
						: 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
				}"
			>
				<div class="flex items-center space-x-2 mb-1">
					<span class="text-lg">{style.icon}</span>
					<span class="font-medium text-sm {
						currentStyle === style.id ? 'text-blue-700' : 'text-gray-700'
					}">
						{style.name}
					</span>
				</div>
				<p class="text-xs {
					currentStyle === style.id ? 'text-blue-600' : 'text-gray-500'
				}">
					{style.description}
				</p>
			</button>
		{/each}
	</div>

	{#if currentStyle === 'auto'}
		<div class="mt-3 p-2 bg-blue-50 border border-blue-200 rounded-lg">
			<p class="text-xs text-blue-800">
				🕐 Automatický režim se mění podle času:
				<br>6-10h: Úsvit | 10-18h: Den | 18-21h: Soumrak | 21-6h: Noc
			</p>
		</div>
	{/if}
</div>
