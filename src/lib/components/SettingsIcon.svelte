<script lang="ts">
	export let onclick: (() => void) | undefined = undefined;
</script>

<!-- Settings Icon -->
<button
	onclick={onclick}
	class="group w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 flex items-center justify-center border border-white/30 hover:bg-white/30"
	aria-label="Otevřít nastavení"
>
	<!-- Settings gear icon -->
	<svg 
		class="w-5 h-5 text-white group-hover:rotate-90 transition-transform duration-300" 
		fill="none" 
		stroke="currentColor" 
		viewBox="0 0 24 24"
	>
		<path 
			stroke-linecap="round" 
			stroke-linejoin="round" 
			stroke-width="2" 
			d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
		/>
		<path 
			stroke-linecap="round" 
			stroke-linejoin="round" 
			stroke-width="2" 
			d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
		/>
	</svg>
</button>
