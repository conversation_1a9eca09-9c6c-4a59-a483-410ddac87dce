<script lang="ts">
	export let isOpen = false;
	export let selectedCategories: string[] = [];
	export let selectedTimeFilters: string[] = [];
	export let onfilterchange: ((categories: string[], timeFilters: string[]) => void) | undefined = undefined;
	export let onclose: (() => void) | undefined = undefined;

	const categories = [
		// Původní kategorie
		{ value: 'toilet', label: 'Veřejné toalety', icon: '🚻', color: '#3B82F6' },
		{ value: 'wifi', label: 'WiFi zdarma', icon: '📶', color: '#10B981' },
		{ value: 'running', label: 'Místa na běhání', icon: '🏃', color: '#F59E0B' },
		{ value: 'park', label: 'Parky', icon: '🌳', color: '#059669' },
		{ value: 'shop', label: 'Obchody/Večerky', icon: '🏪', color: '#8B5CF6' },
		{ value: 'cafe', label: 'Kav<PERSON>rny', icon: '☕', color: '#EF4444' },
		{ value: 'restaurant', label: 'Restaurace/Bary', icon: '🍺', color: '#F97316' },

		// Doprava a mobilita
		{ value: 'transport_stop', label: 'Zastávky MHD', icon: '🚌', color: '#FF6B35' },
		{ value: 'parking', label: 'Parkování', icon: '🚗', color: '#4A90E2' },
		{ value: 'bike_sharing', label: 'Bike sharing', icon: '🚲', color: '#7ED321' },
		{ value: 'gas_station', label: 'Čerpací stanice', icon: '⛽', color: '#F5A623' },

		// Zdraví a sport
		{ value: 'healthcare', label: 'Zdravotnictví', icon: '🏥', color: '#D0021B' },
		{ value: 'pharmacy', label: 'Lékárny', icon: '💊', color: '#50E3C2' },
		{ value: 'fitness', label: 'Fitness centra', icon: '🏋️', color: '#9013FE' },
		{ value: 'swimming', label: 'Bazény a aquacentra', icon: '🏊', color: '#00BCD4' },
		{ value: 'sports', label: 'Sportovní areály', icon: '⚽', color: '#8BC34A' },

		// Služby
		{ value: 'bank', label: 'Banky a bankomaty', icon: '🏦', color: '#607D8B' },
		{ value: 'post', label: 'Zásilky', icon: '📮', color: '#FF9800' },

		// Nákupy
		{ value: 'supermarket', label: 'Supermarkety', icon: '🛒', color: '#E91E63' },
		{ value: 'bakery', label: 'Pekárny', icon: '🥖', color: '#FFEB3B' },
		{ value: 'shopping_center', label: 'Obchodní centra', icon: '🛍️', color: '#9C27B0' }
	];

	const timeFilters = [
		{ value: 'open_now', label: 'Otevřeno nyní', icon: '🕐', color: '#4CAF50' },
		{ value: 'evening_hours', label: 'Otevřeno večer', icon: '🌙', color: '#FF9800' },
		{ value: 'weekend_hours', label: 'Otevřeno o víkendu', icon: '📅', color: '#2196F3' },
		{ value: 'is_24_7', label: '24/7', icon: '⏰', color: '#9C27B0' }
	];

	function toggleCategory(categoryValue: string) {
		if (selectedCategories.includes(categoryValue)) {
			selectedCategories = selectedCategories.filter(c => c !== categoryValue);
		} else {
			selectedCategories = [...selectedCategories, categoryValue];
		}

		if (onfilterchange) {
			onfilterchange(selectedCategories, selectedTimeFilters);
		}
	}

	function toggleTimeFilter(filterValue: string) {
		if (selectedTimeFilters.includes(filterValue)) {
			selectedTimeFilters = selectedTimeFilters.filter(f => f !== filterValue);
		} else {
			selectedTimeFilters = [...selectedTimeFilters, filterValue];
		}

		if (onfilterchange) {
			onfilterchange(selectedCategories, selectedTimeFilters);
		}
	}

	function selectAll() {
		selectedCategories = categories.map(c => c.value);
		if (onfilterchange) {
			onfilterchange(selectedCategories, selectedTimeFilters);
		}
	}

	function clearAll() {
		selectedCategories = [];
		selectedTimeFilters = [];
		if (onfilterchange) {
			onfilterchange(selectedCategories, selectedTimeFilters);
		}
	}

	function handleClose() {
		isOpen = false;
		if (onclose) {
			onclose();
		}
	}

	function handleBackdropClick(event: MouseEvent) {
		if (event.target === event.currentTarget) {
			handleClose();
		}
	}
</script>

{#if isOpen}
	<!-- Backdrop -->
	<div
		class="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
		onclick={handleBackdropClick}
		onkeydown={(e) => e.key === 'Escape' && handleClose()}
		role="dialog"
		aria-modal="true"
		aria-labelledby="filter-title"
		tabindex="0"
	>
		<!-- Popup positioned in top-left -->
		<div class="absolute top-20 left-4 w-80 max-w-[calc(100vw-2rem)]">
			<div class="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/20 p-6 animate-in slide-in-from-left-5 duration-300">
				<!-- Header -->
				<div class="flex items-center justify-between mb-4">
					<div class="flex items-center space-x-2">
						<div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
							<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"/>
							</svg>
						</div>
						<h3 id="filter-title" class="font-semibold text-gray-900">Filtry kategorií</h3>
					</div>
					<button
						onclick={handleClose}
						class="text-gray-400 hover:text-gray-600 p-1 rounded-lg hover:bg-gray-100 transition-colors"
						aria-label="Zavřít"
					>
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
						</svg>
					</button>
				</div>

				<!-- Quick actions -->
				<div class="flex gap-2 mb-4">
					<button
						onclick={selectAll}
						class="flex-1 px-3 py-2 text-xs font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
					>
						Vybrat vše
					</button>
					<button
						onclick={clearAll}
						class="flex-1 px-3 py-2 text-xs font-medium text-gray-700 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors"
					>
						Zrušit vše
					</button>
				</div>

				<!-- Time filters section -->
				<div class="mb-4">
					<h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
						<span class="text-base mr-2">⏰</span>
						Časové filtry
					</h4>
					<div class="grid grid-cols-2 gap-2">
						{#each timeFilters as filter}
							<label class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer group">
								<input
									type="checkbox"
									checked={selectedTimeFilters.includes(filter.value)}
									onchange={() => toggleTimeFilter(filter.value)}
									class="w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-1"
								/>
								<span class="text-sm">{filter.icon}</span>
								<span class="text-xs font-medium text-gray-700 group-hover:text-gray-900">
									{filter.label}
								</span>
							</label>
						{/each}
					</div>
				</div>

				<!-- Categories section -->
				<div class="mb-4">
					<h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
						<span class="text-base mr-2">📍</span>
						Kategorie míst
					</h4>
					<div class="space-y-2 max-h-64 overflow-y-auto">
						{#each categories as category}
							<label class="flex items-center space-x-3 p-3 rounded-xl hover:bg-gray-50 transition-colors cursor-pointer group">
								<input
									type="checkbox"
									checked={selectedCategories.includes(category.value)}
									onchange={() => toggleCategory(category.value)}
									class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
								/>
								<div class="flex items-center space-x-2 flex-1">
									<span class="text-lg">{category.icon}</span>
									<span class="text-sm font-medium text-gray-700 group-hover:text-gray-900">
										{category.label}
									</span>
								</div>
								<div
									class="w-3 h-3 rounded-full border border-gray-200"
									style="background-color: {category.color}"
								></div>
							</label>
						{/each}
					</div>
				</div>

				<!-- Footer info -->
				<div class="mt-4 pt-4 border-t border-gray-200">
					<p class="text-xs text-gray-500 text-center">
						{selectedCategories.length === 0 && selectedTimeFilters.length === 0
							? 'Zobrazují se všechna místa'
							: `Aktivní filtry: ${selectedCategories.length + selectedTimeFilters.length}`
						}
					</p>
				</div>
			</div>
		</div>
	</div>
{/if}

<style>
	@keyframes slide-in-from-left {
		from {
			opacity: 0;
			transform: translateX(-20px);
		}
		to {
			opacity: 1;
			transform: translateX(0);
		}
	}

	.slide-in-from-left-5 {
		animation-name: slide-in-from-left;
	}
</style>
