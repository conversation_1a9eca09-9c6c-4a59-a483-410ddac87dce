<script lang="ts">
	import { themeSettings, updateMapStyle, updateColorScheme, colorSchemes, mapStyles, getAutoMapStyle, type MapStyle, type ColorScheme } from '$lib/stores/theme';

	export let isOpen = false;
	export let onclose: (() => void) | undefined = undefined;
	export let onmapstylechange: ((event: { style: string }) => void) | undefined = undefined;

	// Handle backdrop click
	function handleBackdropClick(event: MouseEvent) {
		if (event.target === event.currentTarget) {
			handleClose();
		}
	}

	// Handle close
	function handleClose() {
		isOpen = false;
		onclose?.();
	}

	// Handle map style change
	function handleMapStyleChange(styleId: MapStyle) {
		updateMapStyle(styleId);

		// Notify parent component about style change
		if (onmapstylechange) {
			const selectedStyle = mapStyles.find(s => s.id === styleId);
			if (selectedStyle) {
				const actualStyle = styleId === 'auto' ? getAutoMapStyle() : selectedStyle.style;
				onmapstylechange({ style: actualStyle });
			}
		}
	}

	// Handle color scheme change
	function handleColorSchemeChange(scheme: ColorScheme) {
		updateColorScheme(scheme);
	}

	// Handle escape key
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			handleClose();
		}
	}
</script>

<svelte:window onkeydown={handleKeydown} />

{#if isOpen}
	<!-- Backdrop with blur -->
	<div
		class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
		onclick={handleBackdropClick}
		onkeydown={handleKeydown}
		role="dialog"
		aria-modal="true"
		aria-labelledby="settings-title"
		tabindex="0"
	>
		<!-- Popup content -->
		<div class="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/20 p-6 w-full max-w-md animate-in slide-in-from-bottom-5 duration-300">
			<!-- Header -->
			<div class="flex items-center justify-between mb-6">
				<div class="flex items-center space-x-3">
					<div class="w-10 h-10 bg-gradient-to-br from-gray-500 to-gray-700 rounded-xl flex items-center justify-center">
						<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
						</svg>
					</div>
					<h2 id="settings-title" class="text-xl font-bold text-gray-900">
						Nastavení
					</h2>
				</div>
				<button
					onclick={handleClose}
					class="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100 transition-colors"
					aria-label="Zavřít nastavení"
				>
					<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
					</svg>
				</button>
			</div>

			<!-- Map Style Section -->
			<div class="mb-6">
				<div class="flex items-center space-x-2 mb-3">
					<div class="w-6 h-6 bg-gradient-to-br from-orange-500 to-pink-600 rounded-lg flex items-center justify-center">
						<span class="text-white text-xs">🗺️</span>
					</div>
					<h3 class="font-semibold text-gray-900 text-sm">Styl mapy</h3>
				</div>

				<div class="grid grid-cols-2 gap-2">
					{#each mapStyles as style}
						<button
							onclick={() => handleMapStyleChange(style.id)}
							class="group p-3 rounded-xl border-2 transition-all duration-200 text-left {
								$themeSettings.mapStyle === style.id
									? 'border-blue-500 bg-blue-50'
									: 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
							}"
						>
							<div class="flex items-center space-x-2 mb-1">
								<span class="text-lg">{style.icon}</span>
								<span class="font-medium text-sm {
									$themeSettings.mapStyle === style.id ? 'text-blue-700' : 'text-gray-700'
								}">
									{style.name}
								</span>
							</div>
							<p class="text-xs {
								$themeSettings.mapStyle === style.id ? 'text-blue-600' : 'text-gray-500'
							}">
								{style.description}
							</p>
						</button>
					{/each}
				</div>

				{#if $themeSettings.mapStyle === 'auto'}
					<div class="mt-3 p-2 bg-blue-50 border border-blue-200 rounded-lg">
						<p class="text-xs text-blue-800">
							🕐 Automatický režim se mění podle času:
							<br>6-10h: Světlý | 10-18h: Standardní | 18-21h: Satelit | 21-6h: Tmavý
						</p>
					</div>
				{/if}
			</div>

			<!-- Color Scheme Section -->
			<div>
				<div class="flex items-center space-x-2 mb-3">
					<div class="w-6 h-6 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
						<span class="text-white text-xs">🎨</span>
					</div>
					<h3 class="font-semibold text-gray-900 text-sm">Barevné schéma</h3>
				</div>

				<div class="grid grid-cols-3 gap-2">
					{#each Object.entries(colorSchemes) as [schemeId, scheme]}
						<button
							onclick={() => handleColorSchemeChange(schemeId as ColorScheme)}
							class="group p-3 rounded-xl border-2 transition-all duration-200 text-center {
								$themeSettings.colorScheme === schemeId
									? 'border-blue-500 bg-blue-50'
									: 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
							}"
						>
							<div class="text-2xl mb-1">{scheme.icon}</div>
							<span class="font-medium text-xs {
								$themeSettings.colorScheme === schemeId ? 'text-blue-700' : 'text-gray-700'
							}">
								{scheme.name}
							</span>
						</button>
					{/each}
				</div>

				<div class="mt-3 p-2 bg-gray-50 border border-gray-200 rounded-lg">
					<p class="text-xs text-gray-600">
						💡 Barevné schéma ovlivňuje vzhled navigace, tlačítek a dalších prvků rozhraní.
					</p>
				</div>
			</div>
		</div>
	</div>
{/if}

<style>
	@keyframes slide-in-from-bottom {
		from {
			opacity: 0;
			transform: translateY(20px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.animate-in {
		animation-duration: 300ms;
		animation-timing-function: ease-out;
		animation-fill-mode: both;
	}

	.slide-in-from-bottom-5 {
		animation-name: slide-in-from-bottom;
	}
</style>
