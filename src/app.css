@import 'tailwindcss';

/* Modern font family */
* {
	font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Custom utilities */
.line-clamp-2 {
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

/* Custom shadows */
.shadow-3xl {
	box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth scrolling */
html {
	scroll-behavior: smooth;
}

/* Slide-in animations */
@keyframes slide-in-from-left {
	from {
		opacity: 0;
		transform: translateX(-20px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes slide-in-from-right {
	from {
		opacity: 0;
		transform: translateX(20px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

.animate-in {
	animation-duration: 300ms;
	animation-timing-function: ease-out;
	animation-fill-mode: both;
}

.slide-in-from-left-5 {
	animation-name: slide-in-from-left;
}

.slide-in-from-right-5 {
	animation-name: slide-in-from-right;
}

.duration-300 {
	animation-duration: 300ms;
}

/* Dynamic theme support */
:root {
	--theme-primary: from-blue-600 via-purple-600 to-blue-800;
	--theme-accent: from-blue-500 to-purple-600;
	--theme-button: from-blue-600 to-purple-600;
}

/* Slide-in animation for bottom */
@keyframes slide-in-from-bottom {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.slide-in-from-bottom-5 {
	animation-name: slide-in-from-bottom;
}
