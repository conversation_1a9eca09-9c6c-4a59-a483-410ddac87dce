<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { getPlaces, getPlacesByCategory, deletePlace, user, type Place } from '$lib/supabase';
	import ConfirmDialog from '$lib/components/ConfirmDialog.svelte';

	let places: Place[] = [];
	let loading = true;
	let error = '';
	let searchTerm = '';
	let selectedCategory = '';
	let showSuccessMessage = false;
	let showDeleteDialog = false;
	let placeToDelete: Place | null = null;

	// Load places on component mount
	onMount(async () => {
		// Check for success parameter in URL
		if (browser) {
			const urlParams = new URLSearchParams(window.location.search);
			if (urlParams.get('success') === 'added') {
				showSuccessMessage = true;
				// Remove the parameter from URL
				window.history.replaceState({}, '', window.location.pathname);
				// Hide message after 5 seconds
				setTimeout(() => {
					showSuccessMessage = false;
				}, 5000);
			}
		}

		await loadPlaces();
	});

	async function loadPlaces() {
		try {
			loading = true;
			error = '';

			if (selectedCategory) {
				places = await getPlacesByCategory(selectedCategory);
			} else {
				places = await getPlaces();
			}
		} catch (err) {
			console.error('Error loading places:', err);
			error = 'Chyba při načítání míst. Zkuste to znovu.';
		} finally {
			loading = false;
		}
	}

	// Filter places based on search term
	$: filteredPlaces = places.filter(place =>
		place.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
		place.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
		place.address?.toLowerCase().includes(searchTerm.toLowerCase())
	);

	// Handle category filter change
	async function handleCategoryChange() {
		await loadPlaces();
	}

	// Show delete confirmation
	function showDeleteConfirmation(place: Place) {
		placeToDelete = place;
		showDeleteDialog = true;
	}

	// Delete place after confirmation
	async function confirmDeletePlace() {
		if (!placeToDelete) return;

		try {
			await deletePlace(placeToDelete.id);
			// Reload places
			await loadPlaces();
			console.log('✅ Place deleted successfully');
		} catch (err) {
			console.error('❌ Error deleting place:', err);
			alert('Chyba při mazání místa');
		} finally {
			placeToDelete = null;
			showDeleteDialog = false;
		}
	}

	// Cancel delete
	function cancelDelete() {
		placeToDelete = null;
		showDeleteDialog = false;
	}

	const categoryIcons: Record<string, string> = {
		// Původní kategorie
		toilet: '🚻',
		wifi: '📶',
		running: '🏃',
		park: '🌳',
		shop: '🏪',
		cafe: '☕',
		restaurant: '🍺',

		// Doprava a mobilita
		transport_stop: '🚌',
		parking: '🚗',
		bike_sharing: '🚲',
		gas_station: '⛽',

		// Zdraví a sport
		healthcare: '🏥',
		pharmacy: '💊',
		fitness: '🏋️',
		swimming: '🏊',
		sports: '⚽',

		// Služby
		bank: '🏦',
		post: '📮',

		// Nákupy
		supermarket: '🛒',
		bakery: '🥖',
		shopping_center: '🛍️'
	};

	const categoryNames: Record<string, string> = {
		// Původní kategorie
		toilet: 'Toalety',
		wifi: 'WiFi',
		running: 'Běhání',
		park: 'Parky',
		shop: 'Obchody',
		cafe: 'Kavárny',
		restaurant: 'Restaurace',

		// Doprava a mobilita
		transport_stop: 'Zastávky MHD',
		parking: 'Parkování',
		bike_sharing: 'Bike sharing',
		gas_station: 'Čerpací stanice',

		// Zdraví a sport
		healthcare: 'Zdravotnictví',
		pharmacy: 'Lékárny',
		fitness: 'Fitness',
		swimming: 'Bazény',
		sports: 'Sport',

		// Služby
		bank: 'Banky',
		post: 'Zásilky',

		// Nákupy
		supermarket: 'Supermarkety',
		bakery: 'Pekárny',
		shopping_center: 'Obchodní centra'
	};
</script>

<svelte:head>
	<title>Místa - Mapa Brna</title>
</svelte:head>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
	<!-- Success message -->
	{#if showSuccessMessage}
		<div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
			<div class="flex">
				<div class="text-green-400">✅</div>
				<div class="ml-3">
					<h3 class="text-sm font-medium text-green-800">Místo bylo úspěšně přidáno!</h3>
					<p class="mt-1 text-sm text-green-700">Děkujeme za přispění do mapy Brna.</p>
				</div>
				<button
					onclick={() => showSuccessMessage = false}
					class="ml-auto text-green-400 hover:text-green-600"
				>
					✕
				</button>
			</div>
		</div>
	{/if}

	<div class="mb-8">
		<!-- Header with gradient -->
		<div class="text-center mb-8">
			<h1 class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
				Všechna místa
			</h1>
			<p class="text-gray-600">Objevte užitečná místa v Brně</p>
		</div>

		<!-- Search and filters -->
		<div class="bg-white/80 backdrop-blur-md rounded-2xl shadow-xl border border-white/20 p-6 mb-6">
			<div class="flex flex-col lg:flex-row gap-4">
				<div class="flex-1">
					<label for="search-input" class="block text-sm font-medium text-gray-700 mb-2">Vyhledávání</label>
					<div class="relative">
						<input
							id="search-input"
							type="text"
							placeholder="Hledat podle názvu, popisu nebo adresy..."
							bind:value={searchTerm}
							class="w-full px-4 py-3 pl-10 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
						/>
						<svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
						</svg>
					</div>
				</div>
				<div class="lg:w-64">
					<label for="category-select" class="block text-sm font-medium text-gray-700 mb-2">Kategorie</label>
					<select
						id="category-select"
						bind:value={selectedCategory}
						onchange={handleCategoryChange}
						class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
					>
						<option value="">Všechny kategorie</option>
						<!-- Původní kategorie -->
						<option value="toilet">🚻 Toalety</option>
						<option value="wifi">📶 WiFi</option>
						<option value="running">🏃 Běhání</option>
						<option value="park">🌳 Parky</option>
						<option value="shop">🏪 Obchody</option>
						<option value="cafe">☕ Kavárny</option>
						<option value="restaurant">🍺 Restaurace</option>

						<!-- Doprava a mobilita -->
						<option value="transport_stop">🚌 Zastávky MHD</option>
						<option value="parking">🚗 Parkování</option>
						<option value="bike_sharing">🚲 Bike sharing</option>
						<option value="gas_station">⛽ Čerpací stanice</option>

						<!-- Zdraví a sport -->
						<option value="healthcare">🏥 Zdravotnictví</option>
						<option value="pharmacy">💊 Lékárny</option>
						<option value="fitness">🏋️ Fitness</option>
						<option value="swimming">🏊 Bazény</option>
						<option value="sports">⚽ Sport</option>

						<!-- Služby -->
						<option value="bank">🏦 Banky</option>
						<option value="post">📮 Zásilky</option>

						<!-- Nákupy -->
						<option value="supermarket">🛒 Supermarkety</option>
						<option value="bakery">🥖 Pekárny</option>
						<option value="shopping_center">🛍️ Obchodní centra</option>
					</select>
				</div>
			</div>

			<!-- Results counter -->
			<div class="mt-4 pt-4 border-t border-gray-200">
				<p class="text-sm text-gray-600">
					Zobrazeno <span class="font-semibold text-blue-600">{filteredPlaces.length}</span>
					z <span class="font-semibold">{places.length}</span> míst
				</p>
			</div>
		</div>
	</div>

	<!-- Loading state -->
	{#if loading}
		<div class="text-center py-12">
			<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
			<p class="text-gray-600">Načítám místa...</p>
		</div>
	{:else if error}
		<!-- Error state -->
		<div class="text-center py-12">
			<div class="text-6xl mb-4">❌</div>
			<h3 class="text-lg font-medium text-gray-900 mb-2">Chyba při načítání</h3>
			<p class="text-gray-500 mb-6">{error}</p>
			<button
				onclick={loadPlaces}
				class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
			>
				Zkusit znovu
			</button>
		</div>
	{:else if filteredPlaces.length === 0}
		<!-- Empty state -->
		<div class="text-center py-12">
			<div class="text-6xl mb-4">🗺️</div>
			<h3 class="text-lg font-medium text-gray-900 mb-2">Žádná místa nenalezena</h3>
			<p class="text-gray-500 mb-6">
				{places.length === 0
					? 'Zatím nebyla přidána žádná místa. Buďte první!'
					: 'Zkuste změnit vyhledávací kritéria.'}
			</p>
			<a
				href="/add"
				class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
			>
				{places.length === 0 ? 'Přidat první místo' : 'Přidat nové místo'}
			</a>
		</div>
	{:else}
		<!-- Places grid -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
			{#each filteredPlaces as place}
				<div class="group bg-white/80 backdrop-blur-md rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300">
					<div class="flex items-start justify-between mb-4">
						<div class="flex items-center space-x-3">
							<div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
								<span class="text-white text-xl">{categoryIcons[place.category]}</span>
							</div>
							<div>
								<span class="text-sm font-medium text-gray-700 bg-gray-100 px-3 py-1 rounded-full">
									{categoryNames[place.category]}
								</span>
							</div>
						</div>
						<div class="flex items-center space-x-1 bg-yellow-50 px-2 py-1 rounded-lg">
							<span class="text-yellow-500">⭐</span>
							<span class="text-sm font-medium text-gray-700">{place.rating || 0}</span>
						</div>
					</div>

					<h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
						{place.name}
					</h3>
					<p class="text-gray-600 text-sm mb-4 line-clamp-2">
						{place.description || 'Bez popisu'}
					</p>

					{#if place.address}
						<div class="flex items-center space-x-2 text-gray-500 text-sm mb-4">
							<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
							</svg>
							<span class="truncate">{place.address}</span>
						</div>
					{/if}

					<div class="flex items-center justify-between pt-4 border-t border-gray-100">
						<span class="text-xs text-gray-500 flex items-center space-x-1">
							<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
							</svg>
							<span>{new Date(place.created_at).toLocaleDateString('cs-CZ')}</span>
						</span>
						<div class="flex items-center space-x-2">
							{#if $user}
								<button
									onclick={() => showDeleteConfirmation(place)}
									class="text-red-500 hover:text-red-700 p-1 rounded-lg hover:bg-red-50 transition-all duration-200 group"
									aria-label="Smazat místo"
								>
									<svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
									</svg>
								</button>
							{/if}
							<button class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center space-x-1 group-hover:translate-x-1 transition-transform">
								<span>Detail</span>
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
								</svg>
							</button>
						</div>
					</div>
				</div>
			{/each}
		</div>
	{/if}
</div>

<!-- Delete confirmation dialog -->
<ConfirmDialog
	bind:isOpen={showDeleteDialog}
	title="Smazat místo"
	message={placeToDelete ? `Opravdu chcete smazat místo "${placeToDelete.name}"? Tato akce je nevratná.` : ''}
	confirmText="Ano, smazat"
	cancelText="Zrušit"
	confirmButtonClass="bg-red-600 hover:bg-red-700 text-white"
	on:confirm={confirmDeletePlace}
	on:cancel={cancelDelete}
/>
