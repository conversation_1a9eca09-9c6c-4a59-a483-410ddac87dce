<script lang="ts">
	import { onMount } from 'svelte';
	import mapboxgl from 'mapbox-gl';
	import { env } from '$env/dynamic/public';
	import { getPlaces, type Place } from '$lib/supabase';
	import SearchIcon from '$lib/components/SearchIcon.svelte';
	import FilterPopup from '$lib/components/FilterPopup.svelte';
	import { themeSettings, getAutoMapStyle, mapStyles, colorSchemes } from '$lib/stores/theme';

	let mapContainer: HTMLDivElement;
	let map: mapboxgl.Map;
	let places = $state<Place[]>([]);
	let markers: mapboxgl.Marker[] = [];
	let filteredPlaces = $state<Place[]>([]);
	let selectedCategories = $state<string[]>([]);
	let showFilterPopup = $state(false);

	async function loadPlaces() {
		try {
			places = await getPlaces();
			console.log('Loaded places:', places.length, places);

			// If no places from database, add test data
			if (places.length === 0) {
				console.log('No places from database, adding test data');
				places = [
					{
						id: 1,
						name: 'Test místo',
						description: 'Testovací místo pro ověření funkčnosti',
						category: 'toilet',
						latitude: 49.1951,
						longitude: 16.6068,
						address: 'Brno centrum',
						rating: 5,
						created_at: new Date().toISOString(),
						updated_at: new Date().toISOString()
					}
				];
			}

			// Initialize filtered places and add markers
			updateFilteredPlaces();
			if (map && places.length > 0) {
				addMarkersToMap();
			}
		} catch (error) {
			console.error('Error loading places:', error);
		}
	}

	// Filter places based on selected categories
	function updateFilteredPlaces() {
		if (selectedCategories.length === 0) {
			filteredPlaces = places;
		} else {
			filteredPlaces = places.filter(place => selectedCategories.includes(place.category));
		}

		// Update markers when filter changes
		if (map) {
			addMarkersToMap();
		}
	}

	// Handle filter changes
	function handleFilterChange(categories: string[]) {
		selectedCategories = categories;
		updateFilteredPlaces();
	}

	// Toggle filter popup
	function toggleFilterPopup() {
		showFilterPopup = !showFilterPopup;
	}

	// Handle map style change
	function handleMapStyleChange(styleUrl: string) {
		if (map) {
			map.setStyle(styleUrl);

			// Re-add 3D buildings layer after style change
			map.once('styledata', () => {
				if (map.isStyleLoaded()) {
					add3DBuildingsLayer();
					// Re-add markers
					if (places.length > 0) {
						addMarkersToMap();
					}
				}
			});
		}
	}

	// Add 3D buildings layer
	function add3DBuildingsLayer() {
		if (!map.getLayer('3d-buildings')) {
			map.addLayer({
				'id': '3d-buildings',
				'source': 'composite',
				'source-layer': 'building',
				'filter': ['==', 'extrude', 'true'],
				'type': 'fill-extrusion',
				'minzoom': 15,
				'paint': {
					'fill-extrusion-color': '#aaa',
					'fill-extrusion-height': [
						'interpolate',
						['linear'],
						['zoom'],
						15,
						0,
						15.05,
						['get', 'height']
					],
					'fill-extrusion-base': [
						'interpolate',
						['linear'],
						['zoom'],
						15,
						0,
						15.05,
						['get', 'min_height']
					],
					'fill-extrusion-opacity': 0.6
				}
			});
		}
	}

	// Get current map style URL based on settings
	function getCurrentMapStyle(): string {
		const currentStyle = $themeSettings.mapStyle;
		if (currentStyle === 'auto') {
			return getAutoMapStyle();
		}
		const style = mapStyles.find(s => s.id === currentStyle);
		return style ? style.style : 'mapbox://styles/mapbox/streets-v12';
	}

	// React to theme changes
	$effect(() => {
		if (map) {
			const newStyle = getCurrentMapStyle();
			handleMapStyleChange(newStyle);
		}
	});

	function addMarkersToMap() {
		const placesToShow = selectedCategories.length === 0 ? places : filteredPlaces;
		console.log('Adding markers to map, places count:', placesToShow.length);
		// Clear existing markers
		markers.forEach(marker => marker.remove());
		markers = [];

		// Add new markers for filtered places
		placesToShow.forEach((place: Place) => {
			const categoryIcons: Record<string, string> = {
				toilet: '🚻',
				wifi: '📶',
				running: '🏃',
				park: '🌳',
				shop: '🏪',
				cafe: '☕',
				restaurant: '🍺'
			};

			// Create popup content
			const popupContent = `
				<div class="p-2">
					<div class="flex items-center space-x-2 mb-2">
						<span class="text-lg">${categoryIcons[place.category] || '📍'}</span>
						<h3 class="font-semibold text-gray-900">${place.name}</h3>
					</div>
					${place.description ? `<p class="text-sm text-gray-600 mb-2">${place.description}</p>` : ''}
					${place.address ? `<p class="text-xs text-gray-500 mb-2">📍 ${place.address}</p>` : ''}
					<div class="flex items-center justify-between">
						<span class="text-xs text-yellow-600">⭐ ${place.rating || 0}</span>
						<a href="/places" class="text-xs text-blue-600 hover:text-blue-800">Detail →</a>
					</div>
				</div>
			`;

			// Create marker
			console.log('Creating marker for place:', place.name, 'at', place.latitude, place.longitude);
			const marker = new mapboxgl.Marker({
				color: place.category === 'toilet' ? '#3B82F6' :
					   place.category === 'wifi' ? '#10B981' :
					   place.category === 'running' ? '#F59E0B' :
					   place.category === 'park' ? '#059669' :
					   place.category === 'shop' ? '#8B5CF6' :
					   place.category === 'cafe' ? '#EF4444' : '#6B7280'
			})
			.setLngLat([place.longitude, place.latitude])
			.setPopup(new mapboxgl.Popup().setHTML(popupContent))
			.addTo(map);

			markers.push(marker);
			console.log('Marker added successfully');
		});
	}

	onMount(() => {
		mapboxgl.accessToken = env.PUBLIC_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw';

		map = new mapboxgl.Map({
			container: mapContainer,
			style: getCurrentMapStyle(),
			center: [16.6068, 49.1951], // Brno coordinates
			zoom: 12,
			pitch: 45, // Úhel náklonu pro 3D efekt
			bearing: 0, // Rotace mapy
			antialias: true // Vyhlazování pro lepší kvalitu
		});

		// Add navigation controls
		map.addControl(new mapboxgl.NavigationControl());

		// Add geolocate control
		map.addControl(
			new mapboxgl.GeolocateControl({
				positionOptions: {
					enableHighAccuracy: true
				},
				trackUserLocation: true,
				showUserHeading: true
			})
		);

		// Load places when map is ready
		map.on('load', () => {
			console.log('Map loaded successfully');
			// Add 3D buildings layer
			add3DBuildingsLayer();

			console.log('Loading places...');
			loadPlaces();
		});

		return () => {
			map?.remove();
		};
	});
</script>

<svelte:head>
	<title>Mapa Brna - Užitečná místa</title>
</svelte:head>

<div class="h-screen relative overflow-hidden">
	<!-- Map container -->
	<div bind:this={mapContainer} class="w-full h-full"></div>

	<!-- Search/Filter Icon - Top Left -->
	<div class="absolute top-4 left-4 z-30">
		<SearchIcon onclick={toggleFilterPopup} colorScheme={$themeSettings.colorScheme} />
	</div>

	<!-- Filter Popup -->
	<FilterPopup
		bind:isOpen={showFilterPopup}
		bind:selectedCategories={selectedCategories}
		onfilterchange={handleFilterChange}
		onclose={() => showFilterPopup = false}
	/>

	<!-- Add place button -->
	<div class="fixed bottom-6 right-6 z-20">
		<a
			href="/add"
			class="group bg-gradient-to-r {colorSchemes[$themeSettings.colorScheme].colors.button} text-white rounded-2xl shadow-2xl hover:shadow-3xl hover:scale-110 transition-all duration-300 flex items-center"
			aria-label="Přidat nové místo"
		>
			<!-- Mobile: just the plus icon centered -->
			<div class="sm:hidden w-14 h-14 flex items-center justify-center">
				<svg class="w-6 h-6 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
				</svg>
			</div>

			<!-- Desktop: icon + text -->
			<div class="hidden sm:flex items-center space-x-2 px-4 py-4">
				<svg class="w-6 h-6 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
				</svg>
				<span class="font-medium">Přidat místo</span>
			</div>
		</a>
	</div>
</div>
