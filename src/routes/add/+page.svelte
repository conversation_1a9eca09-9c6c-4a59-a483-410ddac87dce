<script lang="ts">
	import { onMount } from 'svelte';
	import mapboxgl from 'mapbox-gl';
	import { env } from '$env/dynamic/public';
	import { createPlace, user } from '$lib/supabase';
	import { goto } from '$app/navigation';

	let mapContainer: HTMLDivElement;
	let map: mapboxgl.Map;
	let marker: mapboxgl.Marker;

	// Form data
	let formData = {
		name: '',
		category: '',
		description: '',
		address: '',
		website: '',
		phone: '',
		latitude: 49.1951,
		longitude: 16.6068
	};

	const categories = [
		{ value: 'toilet', label: '🚻 Veřejné toalety', icon: '🚻' },
		{ value: 'wifi', label: '📶 WiFi zdarma', icon: '📶' },
		{ value: 'running', label: '🏃 Místa na běhání', icon: '🏃' },
		{ value: 'park', label: '🌳 Parky', icon: '🌳' },
		{ value: 'shop', label: '🏪 Obchody/Ve<PERSON>ky', icon: '🏪' },
		{ value: 'cafe', label: '☕ Kavárny', icon: '☕' },
		{ value: 'restaurant', label: '🍺 Restaurace/Bary', icon: '🍺' }
	];

	onMount(() => {
		mapboxgl.accessToken = env.PUBLIC_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw';

		map = new mapboxgl.Map({
			container: mapContainer,
			style: 'mapbox://styles/mapbox/streets-v12',
			center: [formData.longitude, formData.latitude],
			zoom: 14,
			pitch: 30, // Menší úhel pro formulář
			bearing: 0,
			antialias: true
		});

		// Add navigation controls
		map.addControl(new mapboxgl.NavigationControl());

		// Create draggable marker
		marker = new mapboxgl.Marker({ draggable: true })
			.setLngLat([formData.longitude, formData.latitude])
			.addTo(map);

		// Update coordinates when marker is dragged
		marker.on('dragend', () => {
			const lngLat = marker.getLngLat();
			formData.latitude = lngLat.lat;
			formData.longitude = lngLat.lng;
		});

		// Add click handler to move marker
		map.on('click', (e) => {
			formData.latitude = e.lngLat.lat;
			formData.longitude = e.lngLat.lng;
			marker.setLngLat([formData.longitude, formData.latitude]);
		});

		return () => {
			map?.remove();
		};
	});

	let submitting = false;
	let submitError = '';

	async function handleSubmit() {
		if (submitting) return;

		try {
			submitting = true;
			submitError = '';

			// Validate required fields
			if (!formData.name.trim()) {
				throw new Error('Název místa je povinný');
			}
			if (!formData.category) {
				throw new Error('Kategorie je povinná');
			}

			// Create place in Supabase
			const newPlace = await createPlace({
				name: formData.name.trim(),
				description: formData.description.trim() || undefined,
				category: formData.category,
				latitude: Number(formData.latitude),
				longitude: Number(formData.longitude),
				address: formData.address.trim() || undefined,
				website: formData.website.trim() || undefined,
				phone: formData.phone.trim() || undefined
			});

			console.log('Place created:', newPlace);

			// Redirect to places page with success message
			goto('/places?success=added');

		} catch (err) {
			console.error('❌ Error creating place:', err);
			console.error('❌ Form data at time of error:', formData);

			if (err instanceof Error) {
				submitError = err.message;
				console.error('❌ Error message:', err.message);
				console.error('❌ Error stack:', err.stack);
			} else {
				submitError = 'Neočekávaná chyba při ukládání místa';
				console.error('❌ Unknown error type:', typeof err, err);
			}
		} finally {
			submitting = false;
		}
	}
</script>

<svelte:head>
	<title>Přidat místo - Mapa Brna</title>
</svelte:head>

{#if !$user}
	<!-- Not authenticated -->
	<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
		<div class="bg-white/80 backdrop-blur-md rounded-2xl shadow-xl border border-white/20 p-8">
			<div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
				<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
				</svg>
			</div>
			<h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
				Přihlášení vyžadováno
			</h1>
			<p class="text-gray-600 mb-8">
				Pro přidávání nových míst do mapy se musíte nejdříve přihlásit.
				Registrace je zdarma a trvá jen chvilku.
			</p>
			<div class="flex flex-col sm:flex-row gap-4 justify-center">
				<a
					href="/"
					class="px-6 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors"
				>
					← Zpět na mapu
				</a>
				<button
					onclick={() => goto('/')}
					class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
				>
					🔐 Přihlásit se
				</button>
			</div>
		</div>
	</div>
{:else}
	<!-- Authenticated - show form -->
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
	<div class="text-center mb-8">
		<h1 class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
			Přidat nové místo
		</h1>
		<p class="text-gray-600 text-lg">Pomozte rozšířit mapu užitečných míst v Brně</p>
		<div class="mt-4 flex items-center justify-center space-x-2 text-sm text-gray-500">
			<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
			</svg>
			<span>Klikněte na mapu nebo přetáhněte značku pro nastavení polohy</span>
		</div>
	</div>

	<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
		<!-- Form -->
		<div class="bg-white/80 backdrop-blur-md rounded-2xl shadow-xl border border-white/20 p-8">
			<!-- Error message -->
			{#if submitError}
				<div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
					<div class="flex">
						<div class="text-red-400">❌</div>
						<div class="ml-3">
							<h3 class="text-sm font-medium text-red-800">Chyba při ukládání</h3>
							<p class="mt-1 text-sm text-red-700">{submitError}</p>
						</div>
					</div>
				</div>
			{/if}

			<form onsubmit={(e) => { e.preventDefault(); handleSubmit(); }} class="space-y-6">
				<!-- Name -->
				<div>
					<label for="name" class="block text-sm font-medium text-gray-700 mb-2">
						Název místa *
					</label>
					<input
						id="name"
						type="text"
						bind:value={formData.name}
						required
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
						placeholder="např. Veřejné WC - Náměstí Svobody"
					/>
				</div>

				<!-- Category -->
				<div>
					<label for="category" class="block text-sm font-medium text-gray-700 mb-2">
						Kategorie *
					</label>
					<select
						id="category"
						bind:value={formData.category}
						required
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					>
						<option value="">Vyberte kategorii</option>
						{#each categories as category}
							<option value={category.value}>{category.label}</option>
						{/each}
					</select>
				</div>

				<!-- Description -->
				<div>
					<label for="description" class="block text-sm font-medium text-gray-700 mb-2">
						Popis
					</label>
					<textarea
						id="description"
						bind:value={formData.description}
						rows="3"
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
						placeholder="Krátký popis místa..."
					></textarea>
				</div>

				<!-- Address -->
				<div>
					<label for="address" class="block text-sm font-medium text-gray-700 mb-2">
						Adresa
					</label>
					<input
						id="address"
						type="text"
						bind:value={formData.address}
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
						placeholder="např. Náměstí Svobody 1, Brno"
					/>
				</div>

				<!-- Contact info -->
				<div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
					<div>
						<label for="website" class="block text-sm font-medium text-gray-700 mb-2">
							Webové stránky
						</label>
						<input
							id="website"
							type="url"
							bind:value={formData.website}
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							placeholder="https://..."
						/>
					</div>
					<div>
						<label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
							Telefon
						</label>
						<input
							id="phone"
							type="tel"
							bind:value={formData.phone}
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							placeholder="+420 123 456 789"
						/>
					</div>
				</div>

				<!-- Coordinates -->
				<div class="grid grid-cols-2 gap-4">
					<div>
						<label for="latitude" class="block text-sm font-medium text-gray-700 mb-2">
							Zeměpisná šířka
						</label>
						<input
							id="latitude"
							type="number"
							step="any"
							bind:value={formData.latitude}
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
						/>
					</div>
					<div>
						<label for="longitude" class="block text-sm font-medium text-gray-700 mb-2">
							Zeměpisná délka
						</label>
						<input
							id="longitude"
							type="number"
							step="any"
							bind:value={formData.longitude}
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
						/>
					</div>
				</div>

				<!-- Submit button -->
				<div class="flex justify-end space-x-4">
					<a
						href="/"
						class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 {submitting ? 'pointer-events-none opacity-50' : ''}"
					>
						Zrušit
					</a>
					<button
						type="submit"
						disabled={submitting}
						class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
					>
						{#if submitting}
							<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
							<span>Ukládám...</span>
						{:else}
							<span>Přidat místo</span>
						{/if}
					</button>
				</div>
			</form>
		</div>

		<!-- Map -->
		<div class="bg-white/80 backdrop-blur-md rounded-2xl shadow-xl border border-white/20 overflow-hidden">
			<div class="p-6 border-b border-gray-100">
				<div class="flex items-center space-x-3 mb-2">
					<div class="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
						<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
						</svg>
					</div>
					<div>
						<h3 class="text-lg font-semibold text-gray-900">Umístění na mapě</h3>
						<p class="text-sm text-gray-600">Přesná poloha místa v Brně</p>
					</div>
				</div>
				<div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
					<p class="text-sm text-blue-800">
						💡 <strong>Tip:</strong> Klikněte na mapu nebo přetáhněte červenou značku pro nastavení přesné polohy
					</p>
				</div>
			</div>
			<div bind:this={mapContainer} class="h-96"></div>
		</div>
	</div>
</div>
{/if}
