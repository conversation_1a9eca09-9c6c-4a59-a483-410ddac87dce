-- Sample data for Mapa Brna
-- Run this after schema.sql to populate the database with test data

-- Insert sample places
INSERT INTO places (name, description, category, latitude, longitude, address, website, phone) VALUES
-- Toilets
('Veřejné WC - Náměstí Svobody', '<PERSON><PERSON><PERSON> veřejné toalety v centru města, otevřené 24/7', 'toilet', 49.1951, 16.6068, 'Náměst<PERSON> Svobody, 602 00 Brno-střed', NULL, NULL),
('WC - <PERSON>lavní nádraží', 'Toalety na hlavním nádraží, placené 10 Kč', 'toilet', 49.1906, 16.6129, 'Nádražní 1, 602 00 Brno-střed', 'https://www.cd.cz', NULL),
('Veřejné <PERSON> - <PERSON> trh', '<PERSON>zplatné toalety u Zelného trhu', 'toilet', 49.1919, 16.6089, '<PERSON><PERSON><PERSON><PERSON> trh, 602 00 Brno-střed', NULL, NULL),

-- WiFi spots
('WiFi zdarma - Kavárna Slavia', 'R<PERSON>lé WiFi připojení zdarma pro z<PERSON>azn<PERSON>y', 'wifi', 49.1925, 16.6077, '<PERSON><PERSON>n<PERSON> 15, 602 00 <PERSON>rno-střed', 'https://www.kavarna-slavia.cz', '+420 542 211 234'),
('<PERSON>ěstsk<PERSON> knihovna - WiFi', '<PERSON>zplatné WiFi v cel<PERSON> budově knihovny', 'wifi', 49.1947, 16.6084, 'Kobližná 4, 601 87 Brno-střed', 'https://www.kjm.cz', '+420 542 531 111'),
('McDonald\'s - WiFi zdarma', 'Rychlé WiFi připojení pro zákazníky', 'wifi', 49.1958, 16.6081, 'Česká 5, 602 00 Brno-střed', 'https://www.mcdonalds.cz', NULL),

-- Running spots
('Běžecká trasa - Lužánky', 'Krásná běžecká trasa parkem s měkkým povrchem, délka 2.5 km', 'running', 49.2089, 16.5989, 'Park Lužánky, 602 00 Brno-střed', NULL, NULL),
('Běhání - Špilberk', 'Náročnější trasa s výhledy na město, délka 3 km', 'running', 49.1942, 16.5988, 'Špilberk, 662 24 Brno-střed', 'https://www.spilberk.cz', NULL),
('Běžecká dráha - Kraví hora', 'Asfaltová dráha s označením vzdáleností', 'running', 49.2156, 16.5756, 'Kraví hora, 616 00 Brno-Žabovřesky', NULL, NULL),

-- Parks
('Park Lužánky', 'Největší park v centru Brna s dětským hřištěm', 'park', 49.2089, 16.5989, 'Park Lužánky, 602 00 Brno-střed', NULL, NULL),
('Denisovy sady', 'Klidný park s výhledem na město', 'park', 49.1969, 16.5967, 'Denisovy sady, 602 00 Brno-střed', NULL, NULL),
('Špilberk - hradní park', 'Historický park kolem hradu Špilberk', 'park', 49.1942, 16.5988, 'Špilberk, 662 24 Brno-střed', 'https://www.spilberk.cz', '+420 542 123 611'),

-- Shops
('Večerka U Jakuba', 'Otevřeno do 22:00, základní potraviny', 'shop', 49.1978, 16.6045, 'Jakubská 5, 602 00 Brno-střed', NULL, '+420 776 123 456'),
('Potraviny COOP', 'Malý supermarket, otevřeno do 21:00', 'shop', 49.1934, 16.6123, 'Masarykova 8, 602 00 Brno-střed', NULL, '+420 542 234 567'),
('Večerka Tesco Express', 'Rychlé nákupy, otevřeno do 23:00', 'shop', 49.1967, 16.6089, 'Česká 12, 602 00 Brno-střed', 'https://www.tesco.cz', NULL),

-- Cafes
('Kavárna Slavia', 'Tradiční kavárna s výbornou kávou', 'cafe', 49.1925, 16.6077, 'Solniční 15, 602 00 Brno-střed', 'https://www.kavarna-slavia.cz', '+420 542 211 234'),
('Café Savoy', 'Stylová kavárna s domácími zákusky', 'cafe', 49.1945, 16.6098, 'Náměstí Svobody 17, 602 00 Brno-střed', NULL, '+420 543 123 789'),
('Coffee Source', 'Speciality coffee a fresh roasted beans', 'cafe', 49.1956, 16.6067, 'Orlí 16, 602 00 Brno-střed', 'https://www.coffesource.cz', '+420 775 987 654'),

-- Restaurants
('Restaurace Borgo Agnese', 'Italská kuchyně v centru města', 'restaurant', 49.1923, 16.6089, 'Kopečná 43, 602 00 Brno-střed', 'https://www.borgoagnese.cz', '+420 542 211 234'),
('Pivnice Pegas', 'Tradiční česká kuchyně s vlastním pivem', 'restaurant', 49.1934, 16.6078, 'Jakubská 4, 602 00 Brno-střed', 'https://www.pegas.cz', '+420 542 210 104'),
('Bar Který Neexistuje', 'Cocktail bar s jedinečnou atmosférou', 'restaurant', 49.1967, 16.6089, 'Údolní 9, 602 00 Brno-střed', NULL, '+420 776 543 210');

-- Insert sample opening hours
INSERT INTO opening_hours (place_id, day_of_week, open_time, close_time, is_closed) VALUES
-- Kavárna Slavia (place_id = 4)
(4, 1, '08:00', '22:00', false), -- Monday
(4, 2, '08:00', '22:00', false), -- Tuesday
(4, 3, '08:00', '22:00', false), -- Wednesday
(4, 4, '08:00', '22:00', false), -- Thursday
(4, 5, '08:00', '23:00', false), -- Friday
(4, 6, '09:00', '23:00', false), -- Saturday
(4, 0, '09:00', '21:00', false), -- Sunday

-- Večerka U Jakuba (place_id = 13)
(13, 1, '06:00', '22:00', false), -- Monday
(13, 2, '06:00', '22:00', false), -- Tuesday
(13, 3, '06:00', '22:00', false), -- Wednesday
(13, 4, '06:00', '22:00', false), -- Thursday
(13, 5, '06:00', '22:00', false), -- Friday
(13, 6, '07:00', '22:00', false), -- Saturday
(13, 0, '08:00', '20:00', false); -- Sunday

-- Insert sample reviews
INSERT INTO reviews (place_id, rating, comment) VALUES
(1, 4, 'Čisté a dobře udržované toalety'),
(1, 5, 'Skvělé umístění v centru'),
(4, 5, 'Výborná káva a rychlé WiFi'),
(4, 4, 'Příjemná atmosféra, trochu hlučné'),
(7, 5, 'Perfektní pro běhání, krásné prostředí'),
(7, 4, 'Dobře značená trasa'),
(13, 3, 'Základní sortiment, vyšší ceny'),
(16, 5, 'Nejlepší káva v Brně!'),
(19, 4, 'Skvělé jídlo, trochu drahé');
