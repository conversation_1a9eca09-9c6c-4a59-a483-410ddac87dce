-- Mapa Brna - Database Schema Update
-- Přidání nových kategorií a rozšíření pro časové filtry
-- Spustit v Supabase SQL Editor po základním schema.sql

-- 1. Přidání nových kategorií do categories tabulky
INSERT INTO categories (name, icon, color, description) VALUES
-- Doprava a mobilita
('transport_stop', '🚌', '#FF6B35', 'Zastávky MHD - autobusy a tramvaje'),
('parking', '🚗', '#4A90E2', 'Parkoviště a parkovací zóny'),
('bike_sharing', '🚲', '#7ED321', '<PERSON>ice sdílen<PERSON>ch kol (Rekola apod.)'),
('gas_station', '⛽', '#F5A623', 'Čerpací stanice a nabíjecí stanice'),

-- Zdraví a sport
('healthcare', '🏥', '#D0021B', 'Nemocnice, polikliniky, ordinace'),
('pharmacy', '💊', '#50E3C2', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a pohotovostní služby'),
('fitness', '🏋️', '#9013FE', 'Fitness centra a posilovny'),
('swimming', '🏊', '#00BCD4', 'Bazény a aquacentra'),
('sports', '⚽', '#8BC34A', 'Sportovní areály a hřiště'),

-- Služby
('bank', '🏦', '#607D8B', 'Banky a bankomaty'),
('post', '📮', '#FF9800', 'Pošta a zásilkové služby'),

-- Nákupy
('supermarket', '🛒', '#E91E63', 'Supermarkety a větší obchody'),
('bakery', '🥖', '#FFEB3B', 'Pekárny a čerstvé pečivo'),
('shopping_center', '🛍️', '#9C27B0', 'Obchodní centra a galerie');

-- 2. Rozšíření places tabulky o nové sloupce pro časové filtry
ALTER TABLE places ADD COLUMN IF NOT EXISTS is_24_7 BOOLEAN DEFAULT FALSE;
ALTER TABLE places ADD COLUMN IF NOT EXISTS evening_hours BOOLEAN DEFAULT FALSE; -- otevřeno po 20:00
ALTER TABLE places ADD COLUMN IF NOT EXISTS weekend_hours BOOLEAN DEFAULT FALSE; -- otevřeno o víkendu

-- 3. Vytvoření indexů pro lepší výkon při filtrování
CREATE INDEX IF NOT EXISTS idx_places_is_24_7 ON places(is_24_7);
CREATE INDEX IF NOT EXISTS idx_places_evening_hours ON places(evening_hours);
CREATE INDEX IF NOT EXISTS idx_places_weekend_hours ON places(weekend_hours);
CREATE INDEX IF NOT EXISTS idx_opening_hours_day_time ON opening_hours(day_of_week, open_time, close_time);

-- 4. Funkce pro automatické nastavení časových příznaků na základě opening_hours
CREATE OR REPLACE FUNCTION update_time_flags()
RETURNS TRIGGER AS $$
DECLARE
    place_record RECORD;
    has_weekend BOOLEAN := FALSE;
    has_evening BOOLEAN := FALSE;
    is_always_open BOOLEAN := FALSE;
BEGIN
    -- Získat informace o místě
    SELECT * INTO place_record FROM places WHERE id = COALESCE(NEW.place_id, OLD.place_id);
    
    -- Zkontrolovat víkendové hodiny (sobota = 6, neděle = 0)
    SELECT EXISTS(
        SELECT 1 FROM opening_hours 
        WHERE place_id = place_record.id 
        AND day_of_week IN (0, 6) 
        AND is_closed = FALSE
    ) INTO has_weekend;
    
    -- Zkontrolovat večerní hodiny (po 20:00)
    SELECT EXISTS(
        SELECT 1 FROM opening_hours 
        WHERE place_id = place_record.id 
        AND close_time >= '20:00'
        AND is_closed = FALSE
    ) INTO has_evening;
    
    -- Zkontrolovat 24/7 (všechny dny v týdnu bez uzavírací doby nebo s velmi dlouhými hodinami)
    SELECT (
        SELECT COUNT(*) FROM opening_hours 
        WHERE place_id = place_record.id 
        AND is_closed = FALSE
    ) = 7 AND (
        SELECT COUNT(*) FROM opening_hours 
        WHERE place_id = place_record.id 
        AND (open_time <= '01:00' AND close_time >= '23:00')
        AND is_closed = FALSE
    ) = 7 INTO is_always_open;
    
    -- Aktualizovat příznaky v places tabulce
    UPDATE places 
    SET 
        weekend_hours = has_weekend,
        evening_hours = has_evening,
        is_24_7 = is_always_open
    WHERE id = place_record.id;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- 5. Trigger pro automatické aktualizace časových příznaků
DROP TRIGGER IF EXISTS update_time_flags_trigger ON opening_hours;
CREATE TRIGGER update_time_flags_trigger
    AFTER INSERT OR UPDATE OR DELETE ON opening_hours
    FOR EACH ROW
    EXECUTE FUNCTION update_time_flags();

-- 6. Funkce pro kontrolu, zda je místo otevřeno nyní
CREATE OR REPLACE FUNCTION is_place_open_now(place_id_param INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
    current_day INTEGER;
    current_time TIME;
    is_open BOOLEAN := FALSE;
BEGIN
    -- Získat aktuální den v týdnu (0 = neděle, 1 = pondělí, ..., 6 = sobota)
    SELECT EXTRACT(DOW FROM NOW()) INTO current_day;
    -- Získat aktuální čas
    SELECT CURRENT_TIME INTO current_time;
    
    -- Zkontrolovat, zda je místo otevřeno
    SELECT EXISTS(
        SELECT 1 FROM opening_hours 
        WHERE place_id = place_id_param
        AND day_of_week = current_day
        AND is_closed = FALSE
        AND open_time <= current_time
        AND close_time >= current_time
    ) INTO is_open;
    
    RETURN is_open;
END;
$$ language 'plpgsql';

-- 7. View pro místa otevřená nyní (pro lepší výkon)
CREATE OR REPLACE VIEW places_open_now AS
SELECT p.*, 
       is_place_open_now(p.id) as is_currently_open
FROM places p;

-- 8. Aktualizace RLS politik pro nové sloupce
-- Politiky zůstávají stejné, protože nové sloupce jsou součástí places tabulky

-- 9. Ukázková data pro nové kategorie
INSERT INTO places (name, description, category, latitude, longitude, address, is_24_7, evening_hours, weekend_hours) VALUES
-- Doprava
('Zastávka Náměstí Svobody', 'Tramvajová zastávka v centru města', 'transport_stop', 49.1951, 16.6068, 'Náměstí Svobody, Brno', FALSE, TRUE, TRUE),
('Parkoviště Janáček', 'Placené parkoviště u kulturního centra', 'parking', 49.1945, 16.6089, 'Janáčkovo náměstí, Brno', TRUE, TRUE, TRUE),
('Rekola stanice - Hlavní nádraží', 'Stanice sdílených kol Rekola', 'bike_sharing', 49.1906, 16.6129, 'Hlavní nádraží, Brno', TRUE, TRUE, TRUE),
('Shell - Masarykova', 'Čerpací stanice s nabíjecími stanicemi', 'gas_station', 49.1934, 16.6123, 'Masarykova 25, Brno', TRUE, TRUE, TRUE),

-- Zdraví a sport
('Fakultní nemocnice Brno', 'Hlavní nemocnice v Brně', 'healthcare', 49.2026, 16.5958, 'Jihlavská 20, Brno', TRUE, TRUE, TRUE),
('Lékárna Dr.Max - Centrum', 'Lékárna v centru města', 'pharmacy', 49.1945, 16.6089, 'Česká 12, Brno', FALSE, TRUE, TRUE),
('Fitness Brno', 'Moderní fitness centrum', 'fitness', 49.1978, 16.6045, 'Orlí 8, Brno', FALSE, TRUE, TRUE),
('Aquapark Kohoutovice', 'Plavecký bazén a wellness', 'swimming', 49.1756, 16.5234, 'Chironova 2, Brno', FALSE, FALSE, TRUE),
('Fotbalové hřiště Lužánky', 'Veřejné fotbalové hřiště', 'sports', 49.2089, 16.5989, 'Park Lužánky, Brno', FALSE, TRUE, TRUE),

-- Služby
('Česká spořitelna - Svoboda', 'Pobočka banky s bankomaty', 'bank', 49.1951, 16.6068, 'Náměstí Svobody 21, Brno', FALSE, FALSE, FALSE),
('Česká pošta - Hlavní', 'Hlavní pošta v centru', 'post', 49.1934, 16.6078, 'Poštovská 8, Brno', FALSE, FALSE, FALSE),

-- Nákupy
('Tesco Extra', 'Velký supermarket', 'supermarket', 49.1967, 16.6089, 'Dornych 4, Brno', FALSE, TRUE, TRUE),
('Pekárna U Anděla', 'Tradiční pekárna s čerstvým pečivem', 'bakery', 49.1925, 16.6077, 'Solniční 12, Brno', FALSE, FALSE, TRUE),
('Galerie Vaňkovka', 'Nákupní centrum v centru', 'shopping_center', 49.1989, 16.6078, 'Křenová 21, Brno', FALSE, TRUE, TRUE);

-- 10. Aktualizace časových příznaků pro existující místa
-- Spustit funkci pro všechna existující místa s otevírací dobou
DO $$
DECLARE
    place_rec RECORD;
BEGIN
    FOR place_rec IN SELECT DISTINCT place_id FROM opening_hours LOOP
        PERFORM update_time_flags() FROM opening_hours WHERE place_id = place_rec.place_id LIMIT 1;
    END LOOP;
END $$;

-- 11. Komentáře pro dokumentaci
COMMENT ON COLUMN places.is_24_7 IS 'Místo otevřené 24 hodin denně, 7 dní v týdnu';
COMMENT ON COLUMN places.evening_hours IS 'Místo otevřené večer (po 20:00)';
COMMENT ON COLUMN places.weekend_hours IS 'Místo otevřené o víkendu';
COMMENT ON FUNCTION is_place_open_now(INTEGER) IS 'Funkce vrací TRUE pokud je místo aktuálně otevřené';
COMMENT ON VIEW places_open_now IS 'View obsahující všechna místa s informací o aktuálním stavu otevření';
