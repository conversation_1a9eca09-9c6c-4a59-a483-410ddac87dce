export declare function objectForEach<PERSON>ey<T extends Record<string, unknown>>(obj: T, callback: (key: keyof T) => void): void;
export declare function objectMapKey<T extends Record<string, unknown>, Return>(obj: T, callback: (key: keyof T) => Return): Return[];
export declare function objectReduceKey<T extends Record<string, unknown>, Accumulator>(obj: T, callback: (acc: Accumulator, key: keyof T) => Accumulator, initial: Accumulator): Accumulator;
//# sourceMappingURL=objectIterators.d.ts.map