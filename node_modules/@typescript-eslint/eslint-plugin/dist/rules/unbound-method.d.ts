interface Config {
    ignoreStatic: boolean;
}
export type Options = [Config];
export type MessageIds = 'unbound' | 'unboundWithoutThisAnnotation';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=unbound-method.d.ts.map