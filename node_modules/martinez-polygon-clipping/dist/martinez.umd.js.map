{"version": 3, "file": "martinez.umd.js", "sources": ["../node_modules/splaytree/index.js", "../src/edge_type.js", "../src/operation.js", "../src/compute_fields.js", "../src/sweep_event.js", "../src/equals.js", "../node_modules/robust-predicates/esm/util.js", "../node_modules/robust-predicates/esm/orient2d.js", "../src/signed_area.js", "../src/compare_events.js", "../src/divide_segment.js", "../src/segment_intersection.js", "../src/possible_intersection.js", "../src/compare_segments.js", "../src/subdivide_segments.js", "../src/contour.js", "../src/connect_edges.js", "../node_modules/tinyqueue/index.js", "../src/fill_queue.js", "../src/index.js", "../index.js"], "sourcesContent": ["function DEFAULT_COMPARE (a, b) { return a > b ? 1 : a < b ? -1 : 0; }\n\nexport default class SplayTree {\n\n  constructor(compare = DEFAULT_COMPARE, noDuplicates = false) {\n    this._compare = compare;\n    this._root = null;\n    this._size = 0;\n    this._noDuplicates = !!noDuplicates;\n  }\n\n\n  rotateLeft(x) {\n    var y = x.right;\n    if (y) {\n      x.right = y.left;\n      if (y.left) y.left.parent = x;\n      y.parent = x.parent;\n    }\n\n    if (!x.parent)                this._root = y;\n    else if (x === x.parent.left) x.parent.left = y;\n    else                          x.parent.right = y;\n    if (y) y.left = x;\n    x.parent = y;\n  }\n\n\n  rotateRight(x) {\n    var y = x.left;\n    if (y) {\n      x.left = y.right;\n      if (y.right) y.right.parent = x;\n      y.parent = x.parent;\n    }\n\n    if (!x.parent)               this._root = y;\n    else if(x === x.parent.left) x.parent.left = y;\n    else                         x.parent.right = y;\n    if (y) y.right = x;\n    x.parent = y;\n  }\n\n\n  _splay(x) {\n    while (x.parent) {\n      var p = x.parent;\n      if (!p.parent) {\n        if (p.left === x) this.rotateRight(p);\n        else              this.rotateLeft(p);\n      } else if (p.left === x && p.parent.left === p) {\n        this.rotateRight(p.parent);\n        this.rotateRight(p);\n      } else if (p.right === x && p.parent.right === p) {\n        this.rotateLeft(p.parent);\n        this.rotateLeft(p);\n      } else if (p.left === x && p.parent.right === p) {\n        this.rotateRight(p);\n        this.rotateLeft(p);\n      } else {\n        this.rotateLeft(p);\n        this.rotateRight(p);\n      }\n    }\n  }\n\n\n  splay(x) {\n    var p, gp, ggp, l, r;\n\n    while (x.parent) {\n      p = x.parent;\n      gp = p.parent;\n\n      if (gp && gp.parent) {\n        ggp = gp.parent;\n        if (ggp.left === gp) ggp.left  = x;\n        else                 ggp.right = x;\n        x.parent = ggp;\n      } else {\n        x.parent = null;\n        this._root = x;\n      }\n\n      l = x.left; r = x.right;\n\n      if (x === p.left) { // left\n        if (gp) {\n          if (gp.left === p) {\n            /* zig-zig */\n            if (p.right) {\n              gp.left = p.right;\n              gp.left.parent = gp;\n            } else gp.left = null;\n\n            p.right   = gp;\n            gp.parent = p;\n          } else {\n            /* zig-zag */\n            if (l) {\n              gp.right = l;\n              l.parent = gp;\n            } else gp.right = null;\n\n            x.left    = gp;\n            gp.parent = x;\n          }\n        }\n        if (r) {\n          p.left = r;\n          r.parent = p;\n        } else p.left = null;\n\n        x.right  = p;\n        p.parent = x;\n      } else { // right\n        if (gp) {\n          if (gp.right === p) {\n            /* zig-zig */\n            if (p.left) {\n              gp.right = p.left;\n              gp.right.parent = gp;\n            } else gp.right = null;\n\n            p.left = gp;\n            gp.parent = p;\n          } else {\n            /* zig-zag */\n            if (r) {\n              gp.left = r;\n              r.parent = gp;\n            } else gp.left = null;\n\n            x.right   = gp;\n            gp.parent = x;\n          }\n        }\n        if (l) {\n          p.right = l;\n          l.parent = p;\n        } else p.right = null;\n\n        x.left   = p;\n        p.parent = x;\n      }\n    }\n  }\n\n\n  replace(u, v) {\n    if (!u.parent) this._root = v;\n    else if (u === u.parent.left) u.parent.left = v;\n    else u.parent.right = v;\n    if (v) v.parent = u.parent;\n  }\n\n\n  minNode(u = this._root) {\n    if (u) while (u.left) u = u.left;\n    return u;\n  }\n\n\n  maxNode(u = this._root) {\n    if (u) while (u.right) u = u.right;\n    return u;\n  }\n\n\n  insert(key, data) {\n    var z = this._root;\n    var p = null;\n    var comp = this._compare;\n    var cmp;\n\n    if (this._noDuplicates) {\n      while (z) {\n        p = z;\n        cmp = comp(z.key, key);\n        if (cmp === 0) return;\n        else if (comp(z.key, key) < 0) z = z.right;\n        else z = z.left;\n      }\n    } else {\n      while (z) {\n        p = z;\n        if (comp(z.key, key) < 0) z = z.right;\n        else z = z.left;\n      }\n    }\n\n    z = { key, data, left: null, right: null, parent: p };\n\n    if (!p)                          this._root = z;\n    else if (comp(p.key, z.key) < 0) p.right = z;\n    else                             p.left  = z;\n\n    this.splay(z);\n    this._size++;\n    return z;\n  }\n\n\n  find (key) {\n    var z    = this._root;\n    var comp = this._compare;\n    while (z) {\n      var cmp = comp(z.key, key);\n      if      (cmp < 0) z = z.right;\n      else if (cmp > 0) z = z.left;\n      else              return z;\n    }\n    return null;\n  }\n\n  /**\n   * Whether the tree contains a node with the given key\n   * @param  {Key} key\n   * @return {boolean} true/false\n   */\n  contains (key) {\n    var node       = this._root;\n    var comparator = this._compare;\n    while (node)  {\n      var cmp = comparator(key, node.key);\n      if      (cmp === 0) return true;\n      else if (cmp < 0)   node = node.left;\n      else                node = node.right;\n    }\n\n    return false;\n  }\n\n\n  remove (key) {\n    var z = this.find(key);\n\n    if (!z) return false;\n\n    this.splay(z);\n\n    if (!z.left) this.replace(z, z.right);\n    else if (!z.right) this.replace(z, z.left);\n    else {\n      var y = this.minNode(z.right);\n      if (y.parent !== z) {\n        this.replace(y, y.right);\n        y.right = z.right;\n        y.right.parent = y;\n      }\n      this.replace(z, y);\n      y.left = z.left;\n      y.left.parent = y;\n    }\n\n    this._size--;\n    return true;\n  }\n\n\n  removeNode(z) {\n    if (!z) return false;\n\n    this.splay(z);\n\n    if (!z.left) this.replace(z, z.right);\n    else if (!z.right) this.replace(z, z.left);\n    else {\n      var y = this.minNode(z.right);\n      if (y.parent !== z) {\n        this.replace(y, y.right);\n        y.right = z.right;\n        y.right.parent = y;\n      }\n      this.replace(z, y);\n      y.left = z.left;\n      y.left.parent = y;\n    }\n\n    this._size--;\n    return true;\n  }\n\n\n  erase (key) {\n    var z = this.find(key);\n    if (!z) return;\n\n    this.splay(z);\n\n    var s = z.left;\n    var t = z.right;\n\n    var sMax = null;\n    if (s) {\n      s.parent = null;\n      sMax = this.maxNode(s);\n      this.splay(sMax);\n      this._root = sMax;\n    }\n    if (t) {\n      if (s) sMax.right = t;\n      else   this._root = t;\n      t.parent = sMax;\n    }\n\n    this._size--;\n  }\n\n  /**\n   * Removes and returns the node with smallest key\n   * @return {?Node}\n   */\n  pop () {\n    var node = this._root, returnValue = null;\n    if (node) {\n      while (node.left) node = node.left;\n      returnValue = { key: node.key, data: node.data };\n      this.remove(node.key);\n    }\n    return returnValue;\n  }\n\n\n  /* eslint-disable class-methods-use-this */\n\n  /**\n   * Successor node\n   * @param  {Node} node\n   * @return {?Node}\n   */\n  next (node) {\n    var successor = node;\n    if (successor) {\n      if (successor.right) {\n        successor = successor.right;\n        while (successor && successor.left) successor = successor.left;\n      } else {\n        successor = node.parent;\n        while (successor && successor.right === node) {\n          node = successor; successor = successor.parent;\n        }\n      }\n    }\n    return successor;\n  }\n\n\n  /**\n   * Predecessor node\n   * @param  {Node} node\n   * @return {?Node}\n   */\n  prev (node) {\n    var predecessor = node;\n    if (predecessor) {\n      if (predecessor.left) {\n        predecessor = predecessor.left;\n        while (predecessor && predecessor.right) predecessor = predecessor.right;\n      } else {\n        predecessor = node.parent;\n        while (predecessor && predecessor.left === node) {\n          node = predecessor;\n          predecessor = predecessor.parent;\n        }\n      }\n    }\n    return predecessor;\n  }\n  /* eslint-enable class-methods-use-this */\n\n\n  /**\n   * @param  {forEachCallback} callback\n   * @return {SplayTree}\n   */\n  forEach(callback) {\n    var current = this._root;\n    var s = [], done = false, i = 0;\n\n    while (!done) {\n      // Reach the left most Node of the current Node\n      if (current) {\n        // Place pointer to a tree node on the stack\n        // before traversing the node's left subtree\n        s.push(current);\n        current = current.left;\n      } else {\n        // BackTrack from the empty subtree and visit the Node\n        // at the top of the stack; however, if the stack is\n        // empty you are done\n        if (s.length > 0) {\n          current = s.pop();\n          callback(current, i++);\n\n          // We have visited the node and its left\n          // subtree. Now, it's right subtree's turn\n          current = current.right;\n        } else done = true;\n      }\n    }\n    return this;\n  }\n\n\n  /**\n   * Walk key range from `low` to `high`. Stops if `fn` returns a value.\n   * @param  {Key}      low\n   * @param  {Key}      high\n   * @param  {Function} fn\n   * @param  {*?}       ctx\n   * @return {SplayTree}\n   */\n  range(low, high, fn, ctx) {\n    const Q = [];\n    const compare = this._compare;\n    let node = this._root, cmp;\n\n    while (Q.length !== 0 || node) {\n      if (node) {\n        Q.push(node);\n        node = node.left;\n      } else {\n        node = Q.pop();\n        cmp = compare(node.key, high);\n        if (cmp > 0) {\n          break;\n        } else if (compare(node.key, low) >= 0) {\n          if (fn.call(ctx, node)) return this; // stop if smth is returned\n        }\n        node = node.right;\n      }\n    }\n    return this;\n  }\n\n  /**\n   * Returns all keys in order\n   * @return {Array<Key>}\n   */\n  keys () {\n    var current = this._root;\n    var s = [], r = [], done = false;\n\n    while (!done) {\n      if (current) {\n        s.push(current);\n        current = current.left;\n      } else {\n        if (s.length > 0) {\n          current = s.pop();\n          r.push(current.key);\n          current = current.right;\n        } else done = true;\n      }\n    }\n    return r;\n  }\n\n\n  /**\n   * Returns `data` fields of all nodes in order.\n   * @return {Array<Value>}\n   */\n  values () {\n    var current = this._root;\n    var s = [], r = [], done = false;\n\n    while (!done) {\n      if (current) {\n        s.push(current);\n        current = current.left;\n      } else {\n        if (s.length > 0) {\n          current = s.pop();\n          r.push(current.data);\n          current = current.right;\n        } else done = true;\n      }\n    }\n    return r;\n  }\n\n\n  /**\n   * Returns node at given index\n   * @param  {number} index\n   * @return {?Node}\n   */\n  at (index) {\n    // removed after a consideration, more misleading than useful\n    // index = index % this.size;\n    // if (index < 0) index = this.size - index;\n\n    var current = this._root;\n    var s = [], done = false, i = 0;\n\n    while (!done) {\n      if (current) {\n        s.push(current);\n        current = current.left;\n      } else {\n        if (s.length > 0) {\n          current = s.pop();\n          if (i === index) return current;\n          i++;\n          current = current.right;\n        } else done = true;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Bulk-load items. Both array have to be same size\n   * @param  {Array<Key>}    keys\n   * @param  {Array<Value>}  [values]\n   * @param  {Boolean}       [presort=false] Pre-sort keys and values, using\n   *                                         tree's comparator. Sorting is done\n   *                                         in-place\n   * @return {AVLTree}\n   */\n  load(keys = [], values = [], presort = false) {\n    if (this._size !== 0) throw new Error('bulk-load: tree is not empty');\n    const size = keys.length;\n    if (presort) sort(keys, values, 0, size - 1, this._compare);\n    this._root = loadRecursive(null, keys, values, 0, size);\n    this._size = size;\n    return this;\n  }\n\n\n  min() {\n    var node = this.minNode(this._root);\n    if (node) return node.key;\n    else      return null;\n  }\n\n\n  max() {\n    var node = this.maxNode(this._root);\n    if (node) return node.key;\n    else      return null;\n  }\n\n  isEmpty() { return this._root === null; }\n  get size() { return this._size; }\n\n\n  /**\n   * Create a tree and load it with items\n   * @param  {Array<Key>}          keys\n   * @param  {Array<Value>?}        [values]\n\n   * @param  {Function?}            [comparator]\n   * @param  {Boolean?}             [presort=false] Pre-sort keys and values, using\n   *                                               tree's comparator. Sorting is done\n   *                                               in-place\n   * @param  {Boolean?}             [noDuplicates=false]   Allow duplicates\n   * @return {SplayTree}\n   */\n  static createTree(keys, values, comparator, presort, noDuplicates) {\n    return new SplayTree(comparator, noDuplicates).load(keys, values, presort);\n  }\n}\n\n\nfunction loadRecursive (parent, keys, values, start, end) {\n  const size = end - start;\n  if (size > 0) {\n    const middle = start + Math.floor(size / 2);\n    const key    = keys[middle];\n    const data   = values[middle];\n    const node   = { key, data, parent };\n    node.left    = loadRecursive(node, keys, values, start, middle);\n    node.right   = loadRecursive(node, keys, values, middle + 1, end);\n    return node;\n  }\n  return null;\n}\n\n\nfunction sort(keys, values, left, right, compare) {\n  if (left >= right) return;\n\n  const pivot = keys[(left + right) >> 1];\n  let i = left - 1;\n  let j = right + 1;\n\n  while (true) {\n    do i++; while (compare(keys[i], pivot) < 0);\n    do j--; while (compare(keys[j], pivot) > 0);\n    if (i >= j) break;\n\n    let tmp = keys[i];\n    keys[i] = keys[j];\n    keys[j] = tmp;\n\n    tmp = values[i];\n    values[i] = values[j];\n    values[j] = tmp;\n  }\n\n  sort(keys, values,  left,     j, compare);\n  sort(keys, values, j + 1, right, compare);\n}\n", "export const NORMAL               = 0;\nexport const NON_CONTRIBUTING     = 1;\nexport const SAME_TRANSITION      = 2;\nexport const DIFFERENT_TRANSITION = 3;\n", "export const INTERSECTION = 0;\nexport const UNION        = 1;\nexport const DIFFERENCE   = 2;\nexport const XOR          = 3;\n", "import {\n  NORMAL,\n  SAME_TRANSITION,\n  DIFFERENT_TRANSITION,\n  NON_CONTRIBUTING\n} from './edge_type';\nimport {\n  INTERSECTION,\n  UNION,\n  DIFFERENCE,\n  XOR\n} from './operation';\n\n/**\n * @param  {SweepEvent} event\n * @param  {SweepEvent} prev\n * @param  {Operation} operation\n */\nexport default function computeFields (event, prev, operation) {\n  // compute inOut and otherInOut fields\n  if (prev === null) {\n    event.inOut      = false;\n    event.otherInOut = true;\n\n  // previous line segment in sweepline belongs to the same polygon\n  } else {\n    if (event.isSubject === prev.isSubject) {\n      event.inOut      = !prev.inOut;\n      event.otherInOut = prev.otherInOut;\n\n    // previous line segment in sweepline belongs to the clipping polygon\n    } else {\n      event.inOut      = !prev.otherInOut;\n      event.otherInOut = prev.isVertical() ? !prev.inOut : prev.inOut;\n    }\n\n    // compute prevInResult field\n    if (prev) {\n      event.prevInResult = (!inResult(prev, operation) || prev.isVertical())\n        ? prev.prevInResult : prev;\n    }\n  }\n\n  // check if the line segment belongs to the Boolean operation\n  let isInResult = inResult(event, operation);\n  if (isInResult) {\n    event.resultTransition = determineResultTransition(event, operation);\n  } else {\n    event.resultTransition = 0;\n  }\n}\n\n\n/* eslint-disable indent */\nfunction inResult(event, operation) {\n  switch (event.type) {\n    case NORMAL:\n      switch (operation) {\n        case INTERSECTION:\n          return !event.otherInOut;\n        case UNION:\n          return event.otherInOut;\n        case DIFFERENCE:\n          // return (event.isSubject && !event.otherInOut) ||\n          //         (!event.isSubject && event.otherInOut);\n          return (event.isSubject && event.otherInOut) ||\n                  (!event.isSubject && !event.otherInOut);\n        case XOR:\n          return true;\n      }\n      break;\n    case SAME_TRANSITION:\n      return operation === INTERSECTION || operation === UNION;\n    case DIFFERENT_TRANSITION:\n      return operation === DIFFERENCE;\n    case NON_CONTRIBUTING:\n      return false;\n  }\n  return false;\n}\n/* eslint-enable indent */\n\n\nfunction determineResultTransition(event, operation) {\n  let thisIn = !event.inOut;\n  let thatIn = !event.otherInOut;\n\n  let isIn;\n  switch (operation) {\n    case INTERSECTION:\n      isIn = thisIn && thatIn; break;\n    case UNION:\n      isIn = thisIn || thatIn; break;\n    case XOR:\n      isIn = thisIn ^ thatIn; break;\n    case DIFFERENCE:\n      if (event.isSubject) {\n        isIn = thisIn && !thatIn;\n      } else {\n        isIn = thatIn && !thisIn;\n      }\n      break;\n  }\n  return isIn ? +1 : -1;\n}\n", "import { NORMAL } from './edge_type';\n\n\nexport default class SweepEvent {\n\n\n  /**\n   * Sweepline event\n   *\n   * @class {SweepEvent}\n   * @param {Array.<Number>}  point\n   * @param {Boolean}         left\n   * @param {SweepEvent=}     otherEvent\n   * @param {Boolean}         isSubject\n   * @param {Number}          edgeType\n   */\n  constructor (point, left, otherEvent, isSubject, edgeType) {\n\n    /**\n     * Is left endpoint?\n     * @type {Boolean}\n     */\n    this.left = left;\n\n    /**\n     * @type {Array.<Number>}\n     */\n    this.point = point;\n\n    /**\n     * Other edge reference\n     * @type {SweepEvent}\n     */\n    this.otherEvent = otherEvent;\n\n    /**\n     * Belongs to source or clipping polygon\n     * @type {Boolean}\n     */\n    this.isSubject = isSubject;\n\n    /**\n     * Edge contribution type\n     * @type {Number}\n     */\n    this.type = edgeType || NORMAL;\n\n\n    /**\n     * In-out transition for the sweepline crossing polygon\n     * @type {Boolean}\n     */\n    this.inOut = false;\n\n\n    /**\n     * @type {Boolean}\n     */\n    this.otherInOut = false;\n\n    /**\n     * Previous event in result?\n     * @type {SweepEvent}\n     */\n    this.prevInResult = null;\n\n    /**\n     * Type of result transition (0 = not in result, +1 = out-in, -1, in-out)\n     * @type {Number}\n     */\n    this.resultTransition = 0;\n\n    // connection step\n\n    /**\n     * @type {Number}\n     */\n    this.otherPos = -1;\n\n    /**\n     * @type {Number}\n     */\n    this.outputContourId = -1;\n\n    this.isExteriorRing = true;   // TODO: Looks unused, remove?\n  }\n\n\n  /**\n   * @param  {Array.<Number>}  p\n   * @return {Boolean}\n   */\n  isBelow (p) {\n    const p0 = this.point, p1 = this.otherEvent.point;\n    return this.left\n      ? (p0[0] - p[0]) * (p1[1] - p[1]) - (p1[0] - p[0]) * (p0[1] - p[1]) > 0\n      // signedArea(this.point, this.otherEvent.point, p) > 0 :\n      : (p1[0] - p[0]) * (p0[1] - p[1]) - (p0[0] - p[0]) * (p1[1] - p[1]) > 0;\n      //signedArea(this.otherEvent.point, this.point, p) > 0;\n  }\n\n\n  /**\n   * @param  {Array.<Number>}  p\n   * @return {Boolean}\n   */\n  isAbove (p) {\n    return !this.isBelow(p);\n  }\n\n\n  /**\n   * @return {Boolean}\n   */\n  isVertical () {\n    return this.point[0] === this.otherEvent.point[0];\n  }\n\n\n  /**\n   * Does event belong to result?\n   * @return {Boolean}\n   */\n  get inResult() {\n    return this.resultTransition !== 0;\n  }\n\n\n  clone () {\n    const copy = new SweepEvent(\n      this.point, this.left, this.otherEvent, this.isSubject, this.type);\n\n    copy.contourId        = this.contourId;\n    copy.resultTransition = this.resultTransition;\n    copy.prevInResult     = this.prevInResult;\n    copy.isExteriorRing   = this.isExteriorRing;\n    copy.inOut            = this.inOut;\n    copy.otherInOut       = this.otherInOut;\n\n    return copy;\n  }\n}\n", "export default function equals(p1, p2) {\n  if (p1[0] === p2[0]) {\n    if (p1[1] === p2[1]) {\n      return true;\n    } else {\n      return false;\n    }\n  }\n  return false;\n}\n\n// const EPSILON = 1e-9;\n// const abs = Math.abs;\n// TODO https://github.com/w8r/martinez/issues/6#issuecomment-262847164\n// Precision problem.\n//\n// module.exports = function equals(p1, p2) {\n//   return abs(p1[0] - p2[0]) <= EPSILON && abs(p1[1] - p2[1]) <= EPSILON;\n// };\n", "export const epsilon = 1.1102230246251565e-16;\nexport const splitter = 134217729;\nexport const resulterrbound = (3 + 8 * epsilon) * epsilon;\n\n// fast_expansion_sum_zeroelim routine from oritinal code\nexport function sum(elen, e, flen, f, h) {\n    let Q, Qnew, hh, bvirt;\n    let enow = e[0];\n    let fnow = f[0];\n    let eindex = 0;\n    let findex = 0;\n    if ((fnow > enow) === (fnow > -enow)) {\n        Q = enow;\n        enow = e[++eindex];\n    } else {\n        Q = fnow;\n        fnow = f[++findex];\n    }\n    let hindex = 0;\n    if (eindex < elen && findex < flen) {\n        if ((fnow > enow) === (fnow > -enow)) {\n            Qnew = enow + Q;\n            hh = Q - (Qnew - enow);\n            enow = e[++eindex];\n        } else {\n            Qnew = fnow + Q;\n            hh = Q - (Qnew - fnow);\n            fnow = f[++findex];\n        }\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        while (eindex < elen && findex < flen) {\n            if ((fnow > enow) === (fnow > -enow)) {\n                Qnew = Q + enow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (enow - bvirt);\n                enow = e[++eindex];\n            } else {\n                Qnew = Q + fnow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n                fnow = f[++findex];\n            }\n            Q = Qnew;\n            if (hh !== 0) {\n                h[hindex++] = hh;\n            }\n        }\n    }\n    while (eindex < elen) {\n        Qnew = Q + enow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (enow - bvirt);\n        enow = e[++eindex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    while (findex < flen) {\n        Qnew = Q + fnow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n        fnow = f[++findex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nexport function sum_three(alen, a, blen, b, clen, c, tmp, out) {\n    return sum(sum(alen, a, blen, b, tmp), tmp, clen, c, out);\n}\n\n// scale_expansion_zeroelim routine from oritinal code\nexport function scale(elen, e, b, h) {\n    let Q, sum, hh, product1, product0;\n    let bvirt, c, ahi, alo, bhi, blo;\n\n    c = splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    let enow = e[0];\n    Q = enow * b;\n    c = splitter * enow;\n    ahi = c - (c - enow);\n    alo = enow - ahi;\n    hh = alo * blo - (Q - ahi * bhi - alo * bhi - ahi * blo);\n    let hindex = 0;\n    if (hh !== 0) {\n        h[hindex++] = hh;\n    }\n    for (let i = 1; i < elen; i++) {\n        enow = e[i];\n        product1 = enow * b;\n        c = splitter * enow;\n        ahi = c - (c - enow);\n        alo = enow - ahi;\n        product0 = alo * blo - (product1 - ahi * bhi - alo * bhi - ahi * blo);\n        sum = Q + product0;\n        bvirt = sum - Q;\n        hh = Q - (sum - bvirt) + (product0 - bvirt);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        Q = product1 + sum;\n        hh = sum - (Q - product1);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nexport function negate(elen, e) {\n    for (let i = 0; i < elen; i++) e[i] = -e[i];\n    return elen;\n}\n\nexport function estimate(elen, e) {\n    let Q = e[0];\n    for (let i = 1; i < elen; i++) Q += e[i];\n    return Q;\n}\n\nexport function vec(n) {\n    return new Float64Array(n);\n}\n", "import {epsilon, splitter, resulterrbound, estimate, vec, sum} from './util.js';\n\nconst ccwerrboundA = (3 + 16 * epsilon) * epsilon;\nconst ccwerrboundB = (2 + 12 * epsilon) * epsilon;\nconst ccwerrboundC = (9 + 64 * epsilon) * epsilon * epsilon;\n\nconst B = vec(4);\nconst C1 = vec(8);\nconst C2 = vec(12);\nconst D = vec(16);\nconst u = vec(4);\n\nfunction orient2dadapt(ax, ay, bx, by, cx, cy, detsum) {\n    let acxtail, acytail, bcxtail, bcytail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    const acx = ax - cx;\n    const bcx = bx - cx;\n    const acy = ay - cy;\n    const bcy = by - cy;\n\n    s1 = acx * bcy;\n    c = splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcx;\n    c = splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    B[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    B[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    B[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    B[3] = u3;\n\n    let det = estimate(4, B);\n    let errbound = ccwerrboundB * detsum;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - acx;\n    acxtail = ax - (acx + bvirt) + (bvirt - cx);\n    bvirt = bx - bcx;\n    bcxtail = bx - (bcx + bvirt) + (bvirt - cx);\n    bvirt = ay - acy;\n    acytail = ay - (acy + bvirt) + (bvirt - cy);\n    bvirt = by - bcy;\n    bcytail = by - (bcy + bvirt) + (bvirt - cy);\n\n    if (acxtail === 0 && acytail === 0 && bcxtail === 0 && bcytail === 0) {\n        return det;\n    }\n\n    errbound = ccwerrboundC * detsum + resulterrbound * Math.abs(det);\n    det += (acx * bcytail + bcy * acxtail) - (acy * bcxtail + bcx * acytail);\n    if (det >= errbound || -det >= errbound) return det;\n\n    s1 = acxtail * bcy;\n    c = splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcx;\n    c = splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C1len = sum(4, B, 4, u, C1);\n\n    s1 = acx * bcytail;\n    c = splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcxtail;\n    c = splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C2len = sum(C1len, C1, 4, u, C2);\n\n    s1 = acxtail * bcytail;\n    c = splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcxtail;\n    c = splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const Dlen = sum(C2len, C2, 4, u, D);\n\n    return D[Dlen - 1];\n}\n\nexport function orient2d(ax, ay, bx, by, cx, cy) {\n    const detleft = (ay - cy) * (bx - cx);\n    const detright = (ax - cx) * (by - cy);\n    const det = detleft - detright;\n\n    if (detleft === 0 || detright === 0 || (detleft > 0) !== (detright > 0)) return det;\n\n    const detsum = Math.abs(detleft + detright);\n    if (Math.abs(det) >= ccwerrboundA * detsum) return det;\n\n    return -orient2dadapt(ax, ay, bx, by, cx, cy, detsum);\n}\n\nexport function orient2dfast(ax, ay, bx, by, cx, cy) {\n    return (ay - cy) * (bx - cx) - (ax - cx) * (by - cy);\n}\n", "import {orient2d} from 'robust-predicates';\n\n/**\n * Signed area of the triangle (p0, p1, p2)\n * @param  {Array.<Number>} p0\n * @param  {Array.<Number>} p1\n * @param  {Array.<Number>} p2\n * @return {Number}\n */\nexport default function signedArea(p0, p1, p2) {\n  const res = orient2d(p0[0], p0[1], p1[0], p1[1], p2[0], p2[1]);\n  if (res > 0) return -1;\n  if (res < 0) return 1;\n  return 0;\n}\n", "import signedArea from './signed_area';\n\n/**\n * @param  {SweepEvent} e1\n * @param  {SweepEvent} e2\n * @return {Number}\n */\nexport default function compareEvents(e1, e2) {\n  const p1 = e1.point;\n  const p2 = e2.point;\n\n  // Different x-coordinate\n  if (p1[0] > p2[0]) return 1;\n  if (p1[0] < p2[0]) return -1;\n\n  // Different points, but same x-coordinate\n  // Event with lower y-coordinate is processed first\n  if (p1[1] !== p2[1]) return p1[1] > p2[1] ? 1 : -1;\n\n  return specialCases(e1, e2, p1, p2);\n}\n\n\n/* eslint-disable no-unused-vars */\nfunction specialCases(e1, e2, p1, p2) {\n  // Same coordinates, but one is a left endpoint and the other is\n  // a right endpoint. The right endpoint is processed first\n  if (e1.left !== e2.left)\n    return e1.left ? 1 : -1;\n\n  // const p2 = e1.otherEvent.point, p3 = e2.otherEvent.point;\n  // const sa = (p1[0] - p3[0]) * (p2[1] - p3[1]) - (p2[0] - p3[0]) * (p1[1] - p3[1])\n  // Same coordinates, both events\n  // are left endpoints or right endpoints.\n  // not collinear\n  if (signedArea(p1, e1.otherEvent.point, e2.otherEvent.point) !== 0) {\n    // the event associate to the bottom segment is processed first\n    return (!e1.isBelow(e2.otherEvent.point)) ? 1 : -1;\n  }\n\n  return (!e1.isSubject && e2.isSubject) ? 1 : -1;\n}\n/* eslint-enable no-unused-vars */\n", "import SweepEvent    from './sweep_event';\nimport equals        from './equals';\nimport compareEvents from './compare_events';\n\n/**\n * @param  {SweepEvent} se\n * @param  {Array.<Number>} p\n * @param  {Queue} queue\n * @return {Queue}\n */\nexport default function divideSegment(se, p, queue)  {\n  const r = new SweepEvent(p, false, se,            se.isSubject);\n  const l = new SweepEvent(p, true,  se.otherEvent, se.isSubject);\n\n  /* eslint-disable no-console */\n  if (equals(se.point, se.otherEvent.point)) {\n    console.warn('what is that, a collapsed segment?', se);\n  }\n  /* eslint-enable no-console */\n\n  r.contourId = l.contourId = se.contourId;\n\n  // avoid a rounding error. The left event would be processed after the right event\n  if (compareEvents(l, se.otherEvent) > 0) {\n    se.otherEvent.left = true;\n    l.left = false;\n  }\n\n  // avoid a rounding error. The left event would be processed after the right event\n  // if (compareEvents(se, r) > 0) {}\n\n  se.otherEvent.otherEvent = l;\n  se.otherEvent = r;\n\n  queue.push(l);\n  queue.push(r);\n\n  return queue;\n}\n", "//const EPS = 1e-9;\n\n/**\n * Finds the magnitude of the cross product of two vectors (if we pretend\n * they're in three dimensions)\n *\n * @param {Object} a First vector\n * @param {Object} b Second vector\n * @private\n * @returns {Number} The magnitude of the cross product\n */\nfunction crossProduct(a, b) {\n  return (a[0] * b[1]) - (a[1] * b[0]);\n}\n\n/**\n * Finds the dot product of two vectors.\n *\n * @param {Object} a First vector\n * @param {Object} b Second vector\n * @private\n * @returns {Number} The dot product\n */\nfunction dotProduct(a, b) {\n  return (a[0] * b[0]) + (a[1] * b[1]);\n}\n\n/**\n * Finds the intersection (if any) between two line segments a and b, given the\n * line segments' end points a1, a2 and b1, b2.\n *\n * This algorithm is based on Schneider and <PERSON><PERSON>.\n * http://www.cimec.org.ar/~ncalvo/Schneider_Eberly.pdf\n * Page 244.\n *\n * @param {Array.<Number>} a1 point of first line\n * @param {Array.<Number>} a2 point of first line\n * @param {Array.<Number>} b1 point of second line\n * @param {Array.<Number>} b2 point of second line\n * @param {Boolean=}       noEndpointTouch whether to skip single touchpoints\n *                                         (meaning connected segments) as\n *                                         intersections\n * @returns {Array.<Array.<Number>>|Null} If the lines intersect, the point of\n * intersection. If they overlap, the two end points of the overlapping segment.\n * Otherwise, null.\n */\nexport default function (a1, a2, b1, b2, noEndpointTouch) {\n  // The algorithm expects our lines in the form P + sd, where P is a point,\n  // s is on the interval [0, 1], and d is a vector.\n  // We are passed two points. P can be the first point of each pair. The\n  // vector, then, could be thought of as the distance (in x and y components)\n  // from the first point to the second point.\n  // So first, let's make our vectors:\n  const va = [a2[0] - a1[0], a2[1] - a1[1]];\n  const vb = [b2[0] - b1[0], b2[1] - b1[1]];\n  // We also define a function to convert back to regular point form:\n\n  /* eslint-disable arrow-body-style */\n\n  function toPoint(p, s, d) {\n    return [\n      p[0] + s * d[0],\n      p[1] + s * d[1]\n    ];\n  }\n\n  /* eslint-enable arrow-body-style */\n\n  // The rest is pretty much a straight port of the algorithm.\n  const e = [b1[0] - a1[0], b1[1] - a1[1]];\n  let kross    = crossProduct(va, vb);\n  let sqrKross = kross * kross;\n  const sqrLenA  = dotProduct(va, va);\n  //const sqrLenB  = dotProduct(vb, vb);\n\n  // Check for line intersection. This works because of the properties of the\n  // cross product -- specifically, two vectors are parallel if and only if the\n  // cross product is the 0 vector. The full calculation involves relative error\n  // to account for possible very small line segments. See Schneider & Eberly\n  // for details.\n  if (sqrKross > 0/* EPS * sqrLenB * sqLenA */) {\n    // If they're not parallel, then (because these are line segments) they\n    // still might not actually intersect. This code checks that the\n    // intersection point of the lines is actually on both line segments.\n    const s = crossProduct(e, vb) / kross;\n    if (s < 0 || s > 1) {\n      // not on line segment a\n      return null;\n    }\n    const t = crossProduct(e, va) / kross;\n    if (t < 0 || t > 1) {\n      // not on line segment b\n      return null;\n    }\n    if (s === 0 || s === 1) {\n      // on an endpoint of line segment a\n      return noEndpointTouch ? null : [toPoint(a1, s, va)];\n    }\n    if (t === 0 || t === 1) {\n      // on an endpoint of line segment b\n      return noEndpointTouch ? null : [toPoint(b1, t, vb)];\n    }\n    return [toPoint(a1, s, va)];\n  }\n\n  // If we've reached this point, then the lines are either parallel or the\n  // same, but the segments could overlap partially or fully, or not at all.\n  // So we need to find the overlap, if any. To do that, we can use e, which is\n  // the (vector) difference between the two initial points. If this is parallel\n  // with the line itself, then the two lines are the same line, and there will\n  // be overlap.\n  //const sqrLenE = dotProduct(e, e);\n  kross = crossProduct(e, va);\n  sqrKross = kross * kross;\n\n  if (sqrKross > 0 /* EPS * sqLenB * sqLenE */) {\n  // Lines are just parallel, not the same. No overlap.\n    return null;\n  }\n\n  const sa = dotProduct(va, e) / sqrLenA;\n  const sb = sa + dotProduct(va, vb) / sqrLenA;\n  const smin = Math.min(sa, sb);\n  const smax = Math.max(sa, sb);\n\n  // this is, essentially, the FindIntersection acting on floats from\n  // Schneider & Eberly, just inlined into this function.\n  if (smin <= 1 && smax >= 0) {\n\n    // overlap on an end point\n    if (smin === 1) {\n      return noEndpointTouch ? null : [toPoint(a1, smin > 0 ? smin : 0, va)];\n    }\n\n    if (smax === 0) {\n      return noEndpointTouch ? null : [toPoint(a1, smax < 1 ? smax : 1, va)];\n    }\n\n    if (noEndpointTouch && smin === 0 && smax === 1) return null;\n\n    // There's overlap on a segment -- two points of intersection. Return both.\n    return [\n      toPoint(a1, smin > 0 ? smin : 0, va),\n      toPoint(a1, smax < 1 ? smax : 1, va)\n    ];\n  }\n\n  return null;\n}\n", "import divideSegment from './divide_segment';\nimport intersection  from './segment_intersection';\nimport equals        from './equals';\nimport compareEvents from './compare_events';\nimport {\n  NON_CONTRIBUTING,\n  SAME_TRANSITION,\n  DIFFERENT_TRANSITION\n} from './edge_type';\n\n/**\n * @param  {SweepEvent} se1\n * @param  {SweepEvent} se2\n * @param  {Queue}      queue\n * @return {Number}\n */\nexport default function possibleIntersection (se1, se2, queue) {\n  // that disallows self-intersecting polygons,\n  // did cost us half a day, so I'll leave it\n  // out of respect\n  // if (se1.isSubject === se2.isSubject) return;\n  const inter = intersection(\n    se1.point, se1.otherEvent.point,\n    se2.point, se2.otherEvent.point\n  );\n\n  const nintersections = inter ? inter.length : 0;\n  if (nintersections === 0) return 0; // no intersection\n\n  // the line segments intersect at an endpoint of both line segments\n  if ((nintersections === 1) &&\n      (equals(se1.point, se2.point) ||\n       equals(se1.otherEvent.point, se2.otherEvent.point))) {\n    return 0;\n  }\n\n  if (nintersections === 2 && se1.isSubject === se2.isSubject) {\n    // if(se1.contourId === se2.contourId){\n    // console.warn('Edges of the same polygon overlap',\n    //   se1.point, se1.otherEvent.point, se2.point, se2.otherEvent.point);\n    // }\n    //throw new Error('Edges of the same polygon overlap');\n    return 0;\n  }\n\n  // The line segments associated to se1 and se2 intersect\n  if (nintersections === 1) {\n\n    // if the intersection point is not an endpoint of se1\n    if (!equals(se1.point, inter[0]) && !equals(se1.otherEvent.point, inter[0])) {\n      divideSegment(se1, inter[0], queue);\n    }\n\n    // if the intersection point is not an endpoint of se2\n    if (!equals(se2.point, inter[0]) && !equals(se2.otherEvent.point, inter[0])) {\n      divideSegment(se2, inter[0], queue);\n    }\n    return 1;\n  }\n\n  // The line segments associated to se1 and se2 overlap\n  const events        = [];\n  let leftCoincide  = false;\n  let rightCoincide = false;\n\n  if (equals(se1.point, se2.point)) {\n    leftCoincide = true; // linked\n  } else if (compareEvents(se1, se2) === 1) {\n    events.push(se2, se1);\n  } else {\n    events.push(se1, se2);\n  }\n\n  if (equals(se1.otherEvent.point, se2.otherEvent.point)) {\n    rightCoincide = true;\n  } else if (compareEvents(se1.otherEvent, se2.otherEvent) === 1) {\n    events.push(se2.otherEvent, se1.otherEvent);\n  } else {\n    events.push(se1.otherEvent, se2.otherEvent);\n  }\n\n  if ((leftCoincide && rightCoincide) || leftCoincide) {\n    // both line segments are equal or share the left endpoint\n    se2.type = NON_CONTRIBUTING;\n    se1.type = (se2.inOut === se1.inOut)\n      ? SAME_TRANSITION : DIFFERENT_TRANSITION;\n\n    if (leftCoincide && !rightCoincide) {\n      // honestly no idea, but changing events selection from [2, 1]\n      // to [0, 1] fixes the overlapping self-intersecting polygons issue\n      divideSegment(events[1].otherEvent, events[0].point, queue);\n    }\n    return 2;\n  }\n\n  // the line segments share the right endpoint\n  if (rightCoincide) {\n    divideSegment(events[0], events[1].point, queue);\n    return 3;\n  }\n\n  // no line segment includes totally the other one\n  if (events[0] !== events[3].otherEvent) {\n    divideSegment(events[0], events[1].point, queue);\n    divideSegment(events[1], events[2].point, queue);\n    return 3;\n  }\n\n  // one line segment includes the other one\n  divideSegment(events[0], events[1].point, queue);\n  divideSegment(events[3].otherEvent, events[2].point, queue);\n\n  return 3;\n}\n", "import signedArea    from './signed_area';\nimport compareEvents from './compare_events';\nimport equals        from './equals';\n\n\n/**\n * @param  {SweepEvent} le1\n * @param  {SweepEvent} le2\n * @return {Number}\n */\nexport default function compareSegments(le1, le2) {\n  if (le1 === le2) return 0;\n\n  // Segments are not collinear\n  if (signedArea(le1.point, le1.otherEvent.point, le2.point) !== 0 ||\n    signedArea(le1.point, le1.otherEvent.point, le2.otherEvent.point) !== 0) {\n\n    // If they share their left endpoint use the right endpoint to sort\n    if (equals(le1.point, le2.point)) return le1.isBelow(le2.otherEvent.point) ? -1 : 1;\n\n    // Different left endpoint: use the left endpoint to sort\n    if (le1.point[0] === le2.point[0]) return le1.point[1] < le2.point[1] ? -1 : 1;\n\n    // has the line segment associated to e1 been inserted\n    // into S after the line segment associated to e2 ?\n    if (compareEvents(le1, le2) === 1) return le2.isAbove(le1.point) ? -1 : 1;\n\n    // The line segment associated to e2 has been inserted\n    // into S after the line segment associated to e1\n    return le1.isBelow(le2.point) ? -1 : 1;\n  }\n\n  if (le1.isSubject === le2.isSubject) { // same polygon\n    let p1 = le1.point, p2 = le2.point;\n    if (p1[0] === p2[0] && p1[1] === p2[1]/*equals(le1.point, le2.point)*/) {\n      p1 = le1.otherEvent.point; p2 = le2.otherEvent.point;\n      if (p1[0] === p2[0] && p1[1] === p2[1]) return 0;\n      else return le1.contourId > le2.contourId ? 1 : -1;\n    }\n  } else { // Segments are collinear, but belong to separate polygons\n    return le1.isSubject ? -1 : 1;\n  }\n\n  return compareEvents(le1, le2) === 1 ? 1 : -1;\n}\n", "import Tree                 from 'splaytree';\nimport computeFields        from './compute_fields';\nimport possibleIntersection from './possible_intersection';\nimport compareSegments      from './compare_segments';\nimport {\n  INTERSECTION,\n  DIFFERENCE\n} from './operation';\n\n\nexport default function subdivide(eventQueue, subject, clipping, sbbox, cbbox, operation) {\n  const sweepLine = new Tree(compareSegments);\n  const sortedEvents = [];\n\n  const rightbound = Math.min(sbbox[2], cbbox[2]);\n\n  let prev, next, begin;\n\n  while (eventQueue.length !== 0) {\n    let event = eventQueue.pop();\n    sortedEvents.push(event);\n\n    // optimization by bboxes for intersection and difference goes here\n    if ((operation === INTERSECTION && event.point[0] > rightbound) ||\n        (operation === DIFFERENCE   && event.point[0] > sbbox[2])) {\n      break;\n    }\n\n    if (event.left) {\n      next  = prev = sweepLine.insert(event);\n      begin = sweepLine.minNode();\n\n      if (prev !== begin) prev = sweepLine.prev(prev);\n      else                prev = null;\n\n      next = sweepLine.next(next);\n\n      const prevEvent = prev ? prev.key : null;\n      let prevprevEvent;\n      computeFields(event, prevEvent, operation);\n      if (next) {\n        if (possibleIntersection(event, next.key, eventQueue) === 2) {\n          computeFields(event, prevEvent, operation);\n          computeFields(next.key, event, operation);\n        }\n      }\n\n      if (prev) {\n        if (possibleIntersection(prev.key, event, eventQueue) === 2) {\n          let prevprev = prev;\n          if (prevprev !== begin) prevprev = sweepLine.prev(prevprev);\n          else                    prevprev = null;\n\n          prevprevEvent = prevprev ? prevprev.key : null;\n          computeFields(prevEvent, prevprevEvent, operation);\n          computeFields(event,     prevEvent,     operation);\n        }\n      }\n    } else {\n      event = event.otherEvent;\n      next = prev = sweepLine.find(event);\n\n      if (prev && next) {\n\n        if (prev !== begin) prev = sweepLine.prev(prev);\n        else                prev = null;\n\n        next = sweepLine.next(next);\n        sweepLine.remove(event);\n\n        if (next && prev) {\n          possibleIntersection(prev.key, next.key, eventQueue);\n        }\n      }\n    }\n  }\n  return sortedEvents;\n}\n", "export default class Contour {\n\n  /**\n   * Contour\n   *\n   * @class {Contour}\n   */\n  constructor() {\n    this.points = [];\n    this.holeIds = [];\n    this.holeOf = null;\n    this.depth = null;\n  }\n\n  isExterior() {\n    return this.holeOf == null;\n  }\n\n}\n", "import compareEvents from './compare_events';\nimport Contour from './contour';\n\n/**\n * @param  {Array.<SweepEvent>} sortedEvents\n * @return {Array.<SweepEvent>}\n */\nfunction orderEvents(sortedEvents) {\n  let event, i, len, tmp;\n  const resultEvents = [];\n  for (i = 0, len = sortedEvents.length; i < len; i++) {\n    event = sortedEvents[i];\n    if ((event.left && event.inResult) ||\n      (!event.left && event.otherEvent.inResult)) {\n      resultEvents.push(event);\n    }\n  }\n  // Due to overlapping edges the resultEvents array can be not wholly sorted\n  let sorted = false;\n  while (!sorted) {\n    sorted = true;\n    for (i = 0, len = resultEvents.length; i < len; i++) {\n      if ((i + 1) < len &&\n        compareEvents(resultEvents[i], resultEvents[i + 1]) === 1) {\n        tmp = resultEvents[i];\n        resultEvents[i] = resultEvents[i + 1];\n        resultEvents[i + 1] = tmp;\n        sorted = false;\n      }\n    }\n  }\n\n\n  for (i = 0, len = resultEvents.length; i < len; i++) {\n    event = resultEvents[i];\n    event.otherPos = i;\n  }\n\n  // imagine, the right event is found in the beginning of the queue,\n  // when his left counterpart is not marked yet\n  for (i = 0, len = resultEvents.length; i < len; i++) {\n    event = resultEvents[i];\n    if (!event.left) {\n      tmp = event.otherPos;\n      event.otherPos = event.otherEvent.otherPos;\n      event.otherEvent.otherPos = tmp;\n    }\n  }\n\n  return resultEvents;\n}\n\n\n/**\n * @param  {Number} pos\n * @param  {Array.<SweepEvent>} resultEvents\n * @param  {Object>}    processed\n * @return {Number}\n */\nfunction nextPos(pos, resultEvents, processed, origPos) {\n  let newPos = pos + 1,\n    p = resultEvents[pos].point,\n    p1;\n  const length = resultEvents.length;\n\n  if (newPos < length)\n    p1 = resultEvents[newPos].point;\n\n  while (newPos < length && p1[0] === p[0] && p1[1] === p[1]) {\n    if (!processed[newPos]) {\n      return newPos;\n    } else {\n      newPos++;\n    }\n    if (newPos < length) {\n      p1 = resultEvents[newPos].point;\n    }\n  }\n\n  newPos = pos - 1;\n\n  while (processed[newPos] && newPos > origPos) {\n    newPos--;\n  }\n\n  return newPos;\n}\n\n\nfunction initializeContourFromContext(event, contours, contourId) {\n  const contour = new Contour();\n  if (event.prevInResult != null) {\n    const prevInResult = event.prevInResult;\n    // Note that it is valid to query the \"previous in result\" for its output contour id,\n    // because we must have already processed it (i.e., assigned an output contour id)\n    // in an earlier iteration, otherwise it wouldn't be possible that it is \"previous in\n    // result\".\n    const lowerContourId = prevInResult.outputContourId;\n    const lowerResultTransition = prevInResult.resultTransition;\n    if (lowerResultTransition > 0) {\n      // We are inside. Now we have to check if the thing below us is another hole or\n      // an exterior contour.\n      const lowerContour = contours[lowerContourId];\n      if (lowerContour.holeOf != null) {\n        // The lower contour is a hole => Connect the new contour as a hole to its parent,\n        // and use same depth.\n        const parentContourId = lowerContour.holeOf;\n        contours[parentContourId].holeIds.push(contourId);\n        contour.holeOf = parentContourId;\n        contour.depth = contours[lowerContourId].depth;\n      } else {\n        // The lower contour is an exterior contour => Connect the new contour as a hole,\n        // and increment depth.\n        contours[lowerContourId].holeIds.push(contourId);\n        contour.holeOf = lowerContourId;\n        contour.depth = contours[lowerContourId].depth + 1;\n      }\n    } else {\n      // We are outside => this contour is an exterior contour of same depth.\n      contour.holeOf = null;\n      contour.depth = contours[lowerContourId].depth;\n    }\n  } else {\n    // There is no lower/previous contour => this contour is an exterior contour of depth 0.\n    contour.holeOf = null;\n    contour.depth = 0;\n  }\n  return contour;\n}\n\n/**\n * @param  {Array.<SweepEvent>} sortedEvents\n * @return {Array.<*>} polygons\n */\nexport default function connectEdges(sortedEvents) {\n  let i, len;\n  const resultEvents = orderEvents(sortedEvents);\n\n  // \"false\"-filled array\n  const processed = {};\n  const contours = [];\n\n  for (i = 0, len = resultEvents.length; i < len; i++) {\n\n    if (processed[i]) {\n      continue;\n    }\n\n    const contourId = contours.length;\n    const contour = initializeContourFromContext(resultEvents[i], contours, contourId);\n\n    // Helper function that combines marking an event as processed with assigning its output contour ID\n    const markAsProcessed = (pos) => {\n      processed[pos] = true;\n      if (pos < resultEvents.length && resultEvents[pos]) {\n        resultEvents[pos].outputContourId = contourId;\n      }\n    };\n\n    let pos = i;\n    let origPos = i;\n\n    const initial = resultEvents[i].point;\n    contour.points.push(initial);\n\n    /* eslint no-constant-condition: \"off\" */\n    while (true) {\n      markAsProcessed(pos);\n\n      pos = resultEvents[pos].otherPos;\n\n      markAsProcessed(pos);\n      contour.points.push(resultEvents[pos].point);\n\n      pos = nextPos(pos, resultEvents, processed, origPos);\n\n      if (pos == origPos || pos >= resultEvents.length || !resultEvents[pos]) {\n        break;\n      }\n    }\n\n    contours.push(contour);\n  }\n\n  return contours;\n}\n", "'use strict';\n\nmodule.exports = TinyQueue;\nmodule.exports.default = TinyQueue;\n\nfunction TinyQueue(data, compare) {\n    if (!(this instanceof TinyQueue)) return new TinyQueue(data, compare);\n\n    this.data = data || [];\n    this.length = this.data.length;\n    this.compare = compare || defaultCompare;\n\n    if (this.length > 0) {\n        for (var i = (this.length >> 1) - 1; i >= 0; i--) this._down(i);\n    }\n}\n\nfunction defaultCompare(a, b) {\n    return a < b ? -1 : a > b ? 1 : 0;\n}\n\nTinyQueue.prototype = {\n\n    push: function (item) {\n        this.data.push(item);\n        this.length++;\n        this._up(this.length - 1);\n    },\n\n    pop: function () {\n        if (this.length === 0) return undefined;\n\n        var top = this.data[0];\n        this.length--;\n\n        if (this.length > 0) {\n            this.data[0] = this.data[this.length];\n            this._down(0);\n        }\n        this.data.pop();\n\n        return top;\n    },\n\n    peek: function () {\n        return this.data[0];\n    },\n\n    _up: function (pos) {\n        var data = this.data;\n        var compare = this.compare;\n        var item = data[pos];\n\n        while (pos > 0) {\n            var parent = (pos - 1) >> 1;\n            var current = data[parent];\n            if (compare(item, current) >= 0) break;\n            data[pos] = current;\n            pos = parent;\n        }\n\n        data[pos] = item;\n    },\n\n    _down: function (pos) {\n        var data = this.data;\n        var compare = this.compare;\n        var halfLength = this.length >> 1;\n        var item = data[pos];\n\n        while (pos < halfLength) {\n            var left = (pos << 1) + 1;\n            var right = left + 1;\n            var best = data[left];\n\n            if (right < this.length && compare(data[right], best) < 0) {\n                left = right;\n                best = data[right];\n            }\n            if (compare(best, item) >= 0) break;\n\n            data[pos] = best;\n            pos = left;\n        }\n\n        data[pos] = item;\n    }\n};\n", "import Queue           from 'tinyqueue';\nimport SweepEvent      from './sweep_event';\nimport compareEvents   from './compare_events';\nimport { DIFFERENCE }  from './operation';\n\nconst max = Math.max;\nconst min = Math.min;\n\nlet contourId = 0;\n\n\nfunction processPolygon(contourOrHole, isSubject, depth, Q, bbox, isExteriorRing) {\n  let i, len, s1, s2, e1, e2;\n  for (i = 0, len = contourOrHole.length - 1; i < len; i++) {\n    s1 = contourOrHole[i];\n    s2 = contourOrHole[i + 1];\n    e1 = new SweepEvent(s1, false, undefined, isSubject);\n    e2 = new SweepEvent(s2, false, e1,        isSubject);\n    e1.otherEvent = e2;\n\n    if (s1[0] === s2[0] && s1[1] === s2[1]) {\n      continue; // skip collapsed edges, or it breaks\n    }\n\n    e1.contourId = e2.contourId = depth;\n    if (!isExteriorRing) {\n      e1.isExteriorRing = false;\n      e2.isExteriorRing = false;\n    }\n    if (compareEvents(e1, e2) > 0) {\n      e2.left = true;\n    } else {\n      e1.left = true;\n    }\n\n    const x = s1[0], y = s1[1];\n    bbox[0] = min(bbox[0], x);\n    bbox[1] = min(bbox[1], y);\n    bbox[2] = max(bbox[2], x);\n    bbox[3] = max(bbox[3], y);\n\n    // Pushing it so the queue is sorted from left to right,\n    // with object on the left having the highest priority.\n    Q.push(e1);\n    Q.push(e2);\n  }\n}\n\n\nexport default function fillQueue(subject, clipping, sbbox, cbbox, operation) {\n  const eventQueue = new Queue(null, compareEvents);\n  let polygonSet, isExteriorRing, i, ii, j, jj; //, k, kk;\n\n  for (i = 0, ii = subject.length; i < ii; i++) {\n    polygonSet = subject[i];\n    for (j = 0, jj = polygonSet.length; j < jj; j++) {\n      isExteriorRing = j === 0;\n      if (isExteriorRing) contourId++;\n      processPolygon(polygonSet[j], true, contourId, eventQueue, sbbox, isExteriorRing);\n    }\n  }\n\n  for (i = 0, ii = clipping.length; i < ii; i++) {\n    polygonSet = clipping[i];\n    for (j = 0, jj = polygonSet.length; j < jj; j++) {\n      isExteriorRing = j === 0;\n      if (operation === DIFFERENCE) isExteriorRing = false;\n      if (isExteriorRing) contourId++;\n      processPolygon(polygonSet[j], false, contourId, eventQueue, cbbox, isExteriorRing);\n    }\n  }\n\n  return eventQueue;\n}\n", "import subdivideSegments from './subdivide_segments';\nimport connectEdges      from './connect_edges';\nimport fillQueue         from './fill_queue';\nimport {\n  INTERSECTION,\n  DIFFERENCE,\n  UNION,\n  XOR\n}        from './operation';\n\nconst EMPTY = [];\n\n\nfunction trivialOperation(subject, clipping, operation) {\n  let result = null;\n  if (subject.length * clipping.length === 0) {\n    if        (operation === INTERSECTION) {\n      result = EMPTY;\n    } else if (operation === DIFFERENCE) {\n      result = subject;\n    } else if (operation === UNION ||\n               operation === XOR) {\n      result = (subject.length === 0) ? clipping : subject;\n    }\n  }\n  return result;\n}\n\n\nfunction compareBBoxes(subject, clipping, sbbox, cbbox, operation) {\n  let result = null;\n  if (sbbox[0] > cbbox[2] ||\n      cbbox[0] > sbbox[2] ||\n      sbbox[1] > cbbox[3] ||\n      cbbox[1] > sbbox[3]) {\n    if        (operation === INTERSECTION) {\n      result = EMPTY;\n    } else if (operation === DIFFERENCE) {\n      result = subject;\n    } else if (operation === UNION ||\n               operation === XOR) {\n      result = subject.concat(clipping);\n    }\n  }\n  return result;\n}\n\n\nexport default function boolean(subject, clipping, operation) {\n  if (typeof subject[0][0][0] === 'number') {\n    subject = [subject];\n  }\n  if (typeof clipping[0][0][0] === 'number') {\n    clipping = [clipping];\n  }\n  let trivial = trivialOperation(subject, clipping, operation);\n  if (trivial) {\n    return trivial === EMPTY ? null : trivial;\n  }\n  const sbbox = [Infinity, Infinity, -Infinity, -Infinity];\n  const cbbox = [Infinity, Infinity, -Infinity, -Infinity];\n\n  // console.time('fill queue');\n  const eventQueue = fillQueue(subject, clipping, sbbox, cbbox, operation);\n  //console.timeEnd('fill queue');\n\n  trivial = compareBBoxes(subject, clipping, sbbox, cbbox, operation);\n  if (trivial) {\n    return trivial === EMPTY ? null : trivial;\n  }\n  // console.time('subdivide edges');\n  const sortedEvents = subdivideSegments(eventQueue, subject, clipping, sbbox, cbbox, operation);\n  //console.timeEnd('subdivide edges');\n\n  // console.time('connect vertices');\n  const contours = connectEdges(sortedEvents, operation);\n  //console.timeEnd('connect vertices');\n\n  // Convert contours to polygons\n  const polygons = [];\n  for (let i = 0; i < contours.length; i++) {\n    let contour = contours[i];\n    if (contour.isExterior()) {\n      // The exterior ring goes first\n      let rings = [contour.points];\n      // Followed by holes if any\n      for (let j = 0; j < contour.holeIds.length; j++) {\n        let holeId = contour.holeIds[j];\n        rings.push(contours[holeId].points);\n      }\n      polygons.push(rings);\n    }\n  }\n\n  return polygons;\n}\n", "import boolean from './src/';\nimport {\n  INTERSECTION,\n  DIFFERENCE,\n  UNION,\n  XOR\n} from './src/operation';\n\nexport function union (subject, clipping) {\n  return boolean(subject, clipping, UNION);\n}\n\nexport function diff (subject, clipping) {\n  return boolean(subject, clipping, DIFFERENCE);\n}\n\nexport function xor (subject, clipping) {\n  return boolean(subject, clipping, XOR);\n}\n\nexport function intersection (subject, clipping) {\n  return boolean(subject, clipping, INTERSECTION);\n}\n\n/**\n * @enum {Number}\n */\nexport const operations = { UNION, DIFFERENCE, INTERSECTION, XOR };\n"], "names": ["const", "let", "prototypeAccessors", "Tree", "Queue", "subdivideSegments", "intersection"], "mappings": ";;;;;;;;;;;;;;;EAAA,SAAS,eAAe,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;AACtE;EACe,IAAM,SAAS,GAE5B,kBAAW,CAAC,OAAyB,EAAE,YAAoB,EAAE;qCAA1C,GAAG;+CAA6B,GAAG;AAAQ;EAChE,EAAI,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;EAC5B,EAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;EACtB,EAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;EACnB,EAAI,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,YAAY,CAAC;EACtC;;4DAAC;AACH;AACA;sBACE,kCAAW,CAAC,EAAE;EAChB,EAAI,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;EACpB,EAAI,IAAI,CAAC,EAAE;EACX,IAAM,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC;EACvB,IAAM,IAAI,CAAC,CAAC,IAAI,IAAE,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAC;EACpC,IAAM,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;EAC1B,GAAK;AACL;EACA,EAAI,IAAI,CAAC,CAAC,CAAC,MAAM,iBAAiB,IAAI,CAAC,KAAK,GAAG,CAAC,GAAC;EACjD,OAAS,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,IAAE,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,GAAC;EACpD,gCAAkC,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,GAAC;EACrD,EAAI,IAAI,CAAC,IAAE,CAAC,CAAC,IAAI,GAAG,CAAC,GAAC;EACtB,EAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;EACf,EAAC;AACH;AACA;sBACE,oCAAY,CAAC,EAAE;EACjB,EAAI,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;EACnB,EAAI,IAAI,CAAC,EAAE;EACX,IAAM,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC;EACvB,IAAM,IAAI,CAAC,CAAC,KAAK,IAAE,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAC;EACtC,IAAM,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;EAC1B,GAAK;AACL;EACA,EAAI,IAAI,CAAC,CAAC,CAAC,MAAM,gBAAgB,IAAI,CAAC,KAAK,GAAG,CAAC,GAAC;EAChD,OAAS,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,IAAE,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,GAAC;EACnD,+BAAiC,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,GAAC;EACpD,EAAI,IAAI,CAAC,IAAE,CAAC,CAAC,KAAK,GAAG,CAAC,GAAC;EACvB,EAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;EACf,EAAC;AACH;AACA;sBACE,0BAAO,CAAC,EAAE;EACZ,EAAI,OAAO,CAAC,CAAC,MAAM,EAAE;EACrB,IAAM,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;EACvB,IAAM,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE;EACrB,MAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,IAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAC;EAC9C,wBAA0B,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAC;EAC7C,KAAO,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,EAAE;EACtD,MAAQ,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;EACnC,MAAQ,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;EAC5B,KAAO,MAAM,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,EAAE;EACxD,MAAQ,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;EAClC,MAAQ,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;EAC3B,KAAO,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,EAAE;EACvD,MAAQ,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;EAC5B,MAAQ,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;EAC3B,KAAO,MAAM;EACb,MAAQ,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;EAC3B,MAAQ,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;EAC5B,KAAO;EACP,GAAK;EACH,EAAC;AACH;AACA;sBACE,wBAAM,CAAC,EAAE;EACX,EAAI,IAAI,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AACzB;EACA,EAAI,OAAO,CAAC,CAAC,MAAM,EAAE;EACrB,IAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;EACnB,IAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC;AACpB;EACA,IAAM,IAAI,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE;EAC3B,MAAQ,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC;EACxB,MAAQ,IAAI,GAAG,CAAC,IAAI,KAAK,EAAE,IAAE,GAAG,CAAC,IAAM,EAAE,CAAC,GAAC;EAC3C,2BAA6B,GAAG,CAAC,KAAK,GAAG,CAAC,GAAC;EAC3C,MAAQ,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC;EACvB,KAAO,MAAM;EACb,MAAQ,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;EACxB,MAAQ,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;EACvB,KAAO;AACP;EACA,IAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;AAC9B;EACA,IAAM,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE;EACxB,MAAQ,IAAI,EAAE,EAAE;EAChB,QAAU,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE;EAC7B;EACA,UAAY,IAAI,CAAC,CAAC,KAAK,EAAE;EACzB,YAAc,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC;EAChC,YAAc,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;EAClC,WAAa,QAAM,EAAE,CAAC,IAAI,GAAG,IAAI,GAAC;AAClC;EACA,UAAY,CAAC,CAAC,KAAK,GAAK,EAAE,CAAC;EAC3B,UAAY,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;EAC1B,SAAW,MAAM;EACjB;EACA,UAAY,IAAI,CAAC,EAAE;EACnB,YAAc,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC;EAC3B,YAAc,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC;EAC5B,WAAa,QAAM,EAAE,CAAC,KAAK,GAAG,IAAI,GAAC;AACnC;EACA,UAAY,CAAC,CAAC,IAAI,IAAM,EAAE,CAAC;EAC3B,UAAY,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;EAC1B,SAAW;EACX,OAAS;EACT,MAAQ,IAAI,CAAC,EAAE;EACf,QAAU,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;EACrB,QAAU,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;EACvB,OAAS,QAAM,CAAC,CAAC,IAAI,GAAG,IAAI,GAAC;AAC7B;EACA,MAAQ,CAAC,CAAC,KAAO,EAAE,CAAC,CAAC;EACrB,MAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;EACrB,KAAO,MAAM;EACb,MAAQ,IAAI,EAAE,EAAE;EAChB,QAAU,IAAI,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE;EAC9B;EACA,UAAY,IAAI,CAAC,CAAC,IAAI,EAAE;EACxB,YAAc,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC;EAChC,YAAc,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;EACnC,WAAa,QAAM,EAAE,CAAC,KAAK,GAAG,IAAI,GAAC;AACnC;EACA,UAAY,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC;EACxB,UAAY,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;EAC1B,SAAW,MAAM;EACjB;EACA,UAAY,IAAI,CAAC,EAAE;EACnB,YAAc,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;EAC1B,YAAc,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC;EAC5B,WAAa,QAAM,EAAE,CAAC,IAAI,GAAG,IAAI,GAAC;AAClC;EACA,UAAY,CAAC,CAAC,KAAK,GAAK,EAAE,CAAC;EAC3B,UAAY,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;EAC1B,SAAW;EACX,OAAS;EACT,MAAQ,IAAI,CAAC,EAAE;EACf,QAAU,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;EACtB,QAAU,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;EACvB,OAAS,QAAM,CAAC,CAAC,KAAK,GAAG,IAAI,GAAC;AAC9B;EACA,MAAQ,CAAC,CAAC,IAAI,GAAK,CAAC,CAAC;EACrB,MAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;EACrB,KAAO;EACP,GAAK;EACH,EAAC;AACH;AACA;sBACE,4BAAQ,CAAC,EAAE,CAAC,EAAE;EAChB,EAAI,IAAI,CAAC,CAAC,CAAC,MAAM,IAAE,IAAI,CAAC,KAAK,GAAG,CAAC,GAAC;EAClC,OAAS,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,IAAE,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,GAAC;EACpD,SAAS,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,GAAC;EAC5B,EAAI,IAAI,CAAC,IAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,GAAC;EAC7B,EAAC;AACH;AACA;sBACE,4BAAQ,CAAc,EAAE;2BAAf,GAAG,IAAI,CAAC;AAAQ;EAC3B,EAAI,IAAI,CAAC,IAAE,OAAO,CAAC,CAAC,IAAI,IAAE,CAAC,GAAG,CAAC,CAAC,IAAI,KAAC;EACrC,EAAI,OAAO,CAAC,CAAC;EACX,EAAC;AACH;AACA;sBACE,4BAAQ,CAAc,EAAE;2BAAf,GAAG,IAAI,CAAC;AAAQ;EAC3B,EAAI,IAAI,CAAC,IAAE,OAAO,CAAC,CAAC,KAAK,IAAE,CAAC,GAAG,CAAC,CAAC,KAAK,KAAC;EACvC,EAAI,OAAO,CAAC,CAAC;EACX,EAAC;AACH;AACA;sBACE,0BAAO,GAAG,EAAE,IAAI,EAAE;EACpB,EAAI,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;EACvB,EAAI,IAAI,CAAC,GAAG,IAAI,CAAC;EACjB,EAAI,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;EAC7B,EAAI,IAAI,GAAG,CAAC;AACZ;EACA,EAAI,IAAI,IAAI,CAAC,aAAa,EAAE;EAC5B,IAAM,OAAO,CAAC,EAAE;EAChB,MAAQ,CAAC,GAAG,CAAC,CAAC;EACd,MAAQ,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;EAC/B,MAAQ,IAAI,GAAG,KAAK,CAAC,IAAE,SAAO;EAC9B,WAAa,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,IAAE,CAAC,GAAG,CAAC,CAAC,KAAK,GAAC;EACnD,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,GAAC;EACxB,KAAO;EACP,GAAK,MAAM;EACX,IAAM,OAAO,CAAC,EAAE;EAChB,MAAQ,CAAC,GAAG,CAAC,CAAC;EACd,MAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,IAAE,CAAC,GAAG,CAAC,CAAC,KAAK,GAAC;EAC9C,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,GAAC;EACxB,KAAO;EACP,GAAK;AACL;EACA,EAAI,CAAC,GAAG,OAAE,GAAG,QAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;AAC1D;EACA,EAAI,IAAI,CAAC,CAAC,2BAA2B,IAAI,CAAC,KAAK,GAAG,CAAC,GAAC;EACpD,OAAS,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAE,CAAC,CAAC,KAAK,GAAG,CAAC,GAAC;EACjD,mCAAqC,CAAC,CAAC,IAAM,EAAE,CAAC,GAAC;AACjD;EACA,EAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EAClB,EAAI,IAAI,CAAC,KAAK,EAAE,CAAC;EACjB,EAAI,OAAO,CAAC,CAAC;EACX,EAAC;AACH;AACA;sBACE,sBAAM,GAAG,EAAE;EACb,EAAI,IAAI,CAAC,IAAM,IAAI,CAAC,KAAK,CAAC;EAC1B,EAAI,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;EAC7B,EAAI,OAAO,CAAC,EAAE;EACd,IAAM,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;EACjC,IAAM,OAAS,GAAG,GAAG,CAAC,IAAE,CAAC,GAAG,CAAC,CAAC,KAAK,GAAC;EACpC,SAAW,IAAI,GAAG,GAAG,CAAC,IAAE,CAAC,GAAG,CAAC,CAAC,IAAI,GAAC;EACnC,sBAAwB,OAAO,CAAC,GAAC;EACjC,GAAK;EACL,EAAI,OAAO,IAAI,CAAC;EACd,EAAC;AACH;EACE;EACF;EACA;EACA;EACA;sBACE,8BAAU,GAAG,EAAE;EACjB,EAAI,IAAI,IAAI,OAAS,IAAI,CAAC,KAAK,CAAC;EAChC,EAAI,IAAI,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC;EACnC,EAAI,OAAO,IAAI,CAAG;EAClB,IAAM,IAAI,GAAG,GAAG,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;EAC1C,IAAM,OAAS,GAAG,KAAK,CAAC,IAAE,OAAO,IAAI,GAAC;EACtC,SAAW,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,GAAC;EAC3C,wBAA0B,IAAI,GAAG,IAAI,CAAC,KAAK,GAAC;EAC5C,GAAK;AACL;EACA,EAAI,OAAO,KAAK,CAAC;EACf,EAAC;AACH;AACA;sBACE,0BAAQ,GAAG,EAAE;EACf,EAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3B;EACA,EAAI,IAAI,CAAC,CAAC,IAAE,OAAO,KAAK,GAAC;AACzB;EACA,EAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB;EACA,EAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,GAAC;EAC1C,OAAS,IAAI,CAAC,CAAC,CAAC,KAAK,IAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAC;EAC/C,OAAS;EACT,IAAM,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;EACpC,IAAM,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;EAC1B,MAAQ,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;EACjC,MAAQ,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;EAC1B,MAAQ,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;EAC3B,KAAO;EACP,IAAM,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACzB,IAAM,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;EACtB,IAAM,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;EACxB,GAAK;AACL;EACA,EAAI,IAAI,CAAC,KAAK,EAAE,CAAC;EACjB,EAAI,OAAO,IAAI,CAAC;EACd,EAAC;AACH;AACA;sBACE,kCAAW,CAAC,EAAE;EAChB,EAAI,IAAI,CAAC,CAAC,IAAE,OAAO,KAAK,GAAC;AACzB;EACA,EAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB;EACA,EAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,GAAC;EAC1C,OAAS,IAAI,CAAC,CAAC,CAAC,KAAK,IAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAC;EAC/C,OAAS;EACT,IAAM,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;EACpC,IAAM,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;EAC1B,MAAQ,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;EACjC,MAAQ,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;EAC1B,MAAQ,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;EAC3B,KAAO;EACP,IAAM,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACzB,IAAM,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;EACtB,IAAM,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;EACxB,GAAK;AACL;EACA,EAAI,IAAI,CAAC,KAAK,EAAE,CAAC;EACjB,EAAI,OAAO,IAAI,CAAC;EACd,EAAC;AACH;AACA;sBACE,wBAAO,GAAG,EAAE;EACd,EAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EAC3B,EAAI,IAAI,CAAC,CAAC,IAAE,SAAO;AACnB;EACA,EAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB;EACA,EAAI,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;EACnB,EAAI,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;AACpB;EACA,EAAI,IAAI,IAAI,GAAG,IAAI,CAAC;EACpB,EAAI,IAAI,CAAC,EAAE;EACX,IAAM,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;EACtB,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;EAC7B,IAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;EACvB,IAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;EACxB,GAAK;EACL,EAAI,IAAI,CAAC,EAAE;EACX,IAAM,IAAI,CAAC,IAAE,IAAI,CAAC,KAAK,GAAG,CAAC,GAAC;EAC5B,WAAa,IAAI,CAAC,KAAK,GAAG,CAAC,GAAC;EAC5B,IAAM,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;EACtB,GAAK;AACL;EACA,EAAI,IAAI,CAAC,KAAK,EAAE,CAAC;EACf,EAAC;AACH;EACE;EACF;EACA;EACA;sBACE,sBAAO;EACT,EAAI,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,WAAW,GAAG,IAAI,CAAC;EAC9C,EAAI,IAAI,IAAI,EAAE;EACd,IAAM,OAAO,IAAI,CAAC,IAAI,IAAE,IAAI,GAAG,IAAI,CAAC,IAAI,GAAC;EACzC,IAAM,WAAW,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;EACvD,IAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EAC5B,GAAK;EACL,EAAI,OAAO,WAAW,CAAC;EACrB,EAAC;AACH;AACA;EACE;AACF;EACE;EACF;EACA;EACA;EACA;sBACE,sBAAM,IAAI,EAAE;EACd,EAAI,IAAI,SAAS,GAAG,IAAI,CAAC;EACzB,EAAI,IAAI,SAAS,EAAE;EACnB,IAAM,IAAI,SAAS,CAAC,KAAK,EAAE;EAC3B,MAAQ,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC;EACpC,MAAQ,OAAO,SAAS,IAAI,SAAS,CAAC,IAAI,IAAE,SAAS,GAAG,SAAS,CAAC,IAAI,GAAC;EACvE,KAAO,MAAM;EACb,MAAQ,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;EAChC,MAAQ,OAAO,SAAS,IAAI,SAAS,CAAC,KAAK,KAAK,IAAI,EAAE;EACtD,QAAU,IAAI,GAAG,SAAS,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC;EACzD,OAAS;EACT,KAAO;EACP,GAAK;EACL,EAAI,OAAO,SAAS,CAAC;EACnB,EAAC;AACH;AACA;EACE;EACF;EACA;EACA;EACA;sBACE,sBAAM,IAAI,EAAE;EACd,EAAI,IAAI,WAAW,GAAG,IAAI,CAAC;EAC3B,EAAI,IAAI,WAAW,EAAE;EACrB,IAAM,IAAI,WAAW,CAAC,IAAI,EAAE;EAC5B,MAAQ,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC;EACvC,MAAQ,OAAO,WAAW,IAAI,WAAW,CAAC,KAAK,IAAE,WAAW,GAAG,WAAW,CAAC,KAAK,GAAC;EACjF,KAAO,MAAM;EACb,MAAQ,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;EAClC,MAAQ,OAAO,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,EAAE;EACzD,QAAU,IAAI,GAAG,WAAW,CAAC;EAC7B,QAAU,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC;EAC3C,OAAS;EACT,KAAO;EACP,GAAK;EACL,EAAI,OAAO,WAAW,CAAC;EACrB,EAAC;EACD;AACF;AACA;EACE;EACF;EACA;EACA;sBACE,4BAAQ,QAAQ,EAAE;EACpB,EAAI,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;EAC7B,EAAI,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;AACpC;EACA,EAAI,OAAO,CAAC,IAAI,EAAE;EAClB;EACA,IAAM,IAAI,OAAO,EAAE;EACnB;EACA;EACA,MAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EACxB,MAAQ,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;EAC/B,KAAO,MAAM;EACb;EACA;EACA;EACA,MAAQ,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;EAC1B,QAAU,OAAO,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;EAC5B,QAAU,QAAQ,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;AACjC;EACA;EACA;EACA,QAAU,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;EAClC,OAAS,QAAM,IAAI,GAAG,IAAI,GAAC;EAC3B,KAAO;EACP,GAAK;EACL,EAAI,OAAO,IAAI,CAAC;EACd,EAAC;AACH;AACA;EACE;EACF;EACA;EACA;EACA;EACA;EACA;EACA;sBACE,wBAAM,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE;EAC5B,EAAIA,IAAM,CAAC,GAAG,EAAE,CAAC;EACjB,EAAIA,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;EAClC,EAAIC,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC;AAC/B;EACA,EAAI,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,EAAE;EACnC,IAAM,IAAI,IAAI,EAAE;EAChB,MAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACrB,MAAQ,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;EACzB,KAAO,MAAM;EACb,MAAQ,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;EACvB,MAAQ,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;EACtC,MAAQ,IAAI,GAAG,GAAG,CAAC,EAAE;EACrB,QAAU,MAAM;EAChB,OAAS,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE;EAChD,QAAU,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAE,OAAO,IAAI,GAAC;EAC9C,OAAS;EACT,MAAQ,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;EAC1B,KAAO;EACP,GAAK;EACL,EAAI,OAAO,IAAI,CAAC;EACd,EAAC;AACH;EACE;EACF;EACA;EACA;sBACE,wBAAQ;EACV,EAAI,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;EAC7B,EAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,GAAG,KAAK,CAAC;AACrC;EACA,EAAI,OAAO,CAAC,IAAI,EAAE;EAClB,IAAM,IAAI,OAAO,EAAE;EACnB,MAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EACxB,MAAQ,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;EAC/B,KAAO,MAAM;EACb,MAAQ,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;EAC1B,QAAU,OAAO,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;EAC5B,QAAU,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;EAC9B,QAAU,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;EAClC,OAAS,QAAM,IAAI,GAAG,IAAI,GAAC;EAC3B,KAAO;EACP,GAAK;EACL,EAAI,OAAO,CAAC,CAAC;EACX,EAAC;AACH;AACA;EACE;EACF;EACA;EACA;sBACE,4BAAU;EACZ,EAAI,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;EAC7B,EAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,GAAG,KAAK,CAAC;AACrC;EACA,EAAI,OAAO,CAAC,IAAI,EAAE;EAClB,IAAM,IAAI,OAAO,EAAE;EACnB,MAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EACxB,MAAQ,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;EAC/B,KAAO,MAAM;EACb,MAAQ,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;EAC1B,QAAU,OAAO,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;EAC5B,QAAU,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;EAC/B,QAAU,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;EAClC,OAAS,QAAM,IAAI,GAAG,IAAI,GAAC;EAC3B,KAAO;EACP,GAAK;EACL,EAAI,OAAO,CAAC,CAAC;EACX,EAAC;AACH;AACA;EACE;EACF;EACA;EACA;EACA;sBACE,kBAAI,KAAK,EAAE;EACb;EACA;EACA;AACA;EACA,EAAI,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;EAC7B,EAAI,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;AACpC;EACA,EAAI,OAAO,CAAC,IAAI,EAAE;EAClB,IAAM,IAAI,OAAO,EAAE;EACnB,MAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EACxB,MAAQ,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;EAC/B,KAAO,MAAM;EACb,MAAQ,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;EAC1B,QAAU,OAAO,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;EAC5B,QAAU,IAAI,CAAC,KAAK,KAAK,IAAE,OAAO,OAAO,GAAC;EAC1C,QAAU,CAAC,EAAE,CAAC;EACd,QAAU,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;EAClC,OAAS,QAAM,IAAI,GAAG,IAAI,GAAC;EAC3B,KAAO;EACP,GAAK;EACL,EAAI,OAAO,IAAI,CAAC;EACd,EAAC;AACH;EACE;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;sBACE,sBAAK,IAAS,EAAE,MAAW,EAAE,OAAe,EAAE;iCAArC,GAAG;qCAAU,GAAG;uCAAW,GAAG;AAAQ;EACjD,EAAI,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,IAAE,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,GAAC;EAC1E,EAAID,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;EAC7B,EAAI,IAAI,OAAO,IAAE,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAC;EAChE,EAAI,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;EAC5D,EAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;EACtB,EAAI,OAAO,IAAI,CAAC;EACd,EAAC;AACH;AACA;sBACE,sBAAM;EACR,EAAI,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACxC,EAAI,IAAI,IAAI,IAAE,OAAO,IAAI,CAAC,GAAG,GAAC;EAC9B,YAAc,OAAO,IAAI,GAAC;EACxB,EAAC;AACH;AACA;sBACE,sBAAM;EACR,EAAI,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACxC,EAAI,IAAI,IAAI,IAAE,OAAO,IAAI,CAAC,GAAG,GAAC;EAC9B,YAAc,OAAO,IAAI,GAAC;EACxB,EAAC;AACH;sBACE,8BAAU,EAAE,OAAO,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,GAAE;EACzC,mBAAI,uBAAO,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,GAAE;AACnC;AACA;EACE;EACF;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACE,UAAO,kCAAW,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE;EACrE,EAAI,OAAO,IAAI,SAAS,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;EAC7E;;;AAEF;AACA;EACA,SAAS,aAAa,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE;EAC1D,EAAEA,IAAM,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC;EAC3B,EAAE,IAAI,IAAI,GAAG,CAAC,EAAE;EAChB,IAAIA,IAAM,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;EAChD,IAAIA,IAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC;EAChC,IAAIA,IAAM,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC;EAClC,IAAIA,IAAM,IAAI,KAAK,OAAE,GAAG,QAAE,IAAI,UAAE,MAAM,EAAE,CAAC;EACzC,IAAI,IAAI,CAAC,IAAI,MAAM,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;EACpE,IAAI,IAAI,CAAC,KAAK,KAAK,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;EACtE,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;EACH,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;AACA;EACA,SAAS,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE;EAClD,EAAE,IAAI,IAAI,IAAI,KAAK,IAAE,SAAO;AAC5B;EACA,EAAEA,IAAM,KAAK,GAAG,IAAI,CAAC,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC;EAC1C,EAAEC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;EACnB,EAAEA,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;AACpB;EACA,EAAE,OAAO,IAAI,EAAE;EACf,IAAI,KAAG,CAAC,EAAE,GAAC,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE;EAChD,IAAI,KAAG,CAAC,EAAE,GAAC,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE;EAChD,IAAI,IAAI,CAAC,IAAI,CAAC,IAAE,QAAM;AACtB;EACA,IAAIA,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;EACtB,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;EACtB,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AAClB;EACA,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;EACpB,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;EAC1B,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;EACpB,GAAG;AACH;EACA,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;EAC5C,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;EAC5C;;EC7lBOD,IAAM,MAAM,iBAAiB,CAAC,CAAC;AACtC,EAAOA,IAAM,gBAAgB,OAAO,CAAC,CAAC;AACtC,EAAOA,IAAM,eAAe,QAAQ,CAAC,CAAC;AACtC,EAAOA,IAAM,oBAAoB,GAAG,CAAC,CAAC;;ECH/BA,IAAM,YAAY,GAAG,CAAC,CAAC;AAC9B,EAAOA,IAAM,KAAK,UAAU,CAAC,CAAC;AAC9B,EAAOA,IAAM,UAAU,KAAK,CAAC,CAAC;AAC9B,EAAOA,IAAM,GAAG,YAAY,CAAC,CAAC;;ECU9B;EACA;EACA;EACA;EACA;AACA,EAAe,SAAS,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE;EAC/D;EACA,EAAE,IAAI,IAAI,KAAK,IAAI,EAAE;EACrB,IAAI,KAAK,CAAC,KAAK,QAAQ,KAAK,CAAC;EAC7B,IAAI,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC;AAC5B;EACA;EACA,GAAG,MAAM;EACT,IAAI,IAAI,KAAK,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,EAAE;EAC5C,MAAM,KAAK,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;EACrC,MAAM,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AACzC;EACA;EACA,KAAK,MAAM;EACX,MAAM,KAAK,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC;EAC1C,MAAM,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;EACtE,KAAK;AACL;EACA;EACA,IAAI,IAAI,IAAI,EAAE;EACd,MAAM,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;EAC3E,UAAU,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;EACnC,KAAK;EACL,GAAG;AACH;EACA;EACA,EAAEC,IAAI,UAAU,GAAG,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;EAC9C,EAAE,IAAI,UAAU,EAAE;EAClB,IAAI,KAAK,CAAC,gBAAgB,GAAG,yBAAyB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;EACzE,GAAG,MAAM;EACT,IAAI,KAAK,CAAC,gBAAgB,GAAG,CAAC,CAAC;EAC/B,GAAG;EACH,CAAC;AACD;AACA;EACA;EACA,SAAS,QAAQ,CAAC,KAAK,EAAE,SAAS,EAAE;EACpC,EAAE,QAAQ,KAAK,CAAC,IAAI;EACpB,IAAI,KAAK,MAAM;EACf,MAAM,QAAQ,SAAS;EACvB,QAAQ,KAAK,YAAY;EACzB,UAAU,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC;EACnC,QAAQ,KAAK,KAAK;EAClB,UAAU,OAAO,KAAK,CAAC,UAAU,CAAC;EAClC,QAAQ,KAAK,UAAU;EACvB;EACA;EACA,UAAU,OAAO,CAAC,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,UAAU;EACrD,mBAAmB,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;EAC1D,QAAQ,KAAK,GAAG;EAChB,UAAU,OAAO,IAAI,CAAC;EACtB,OAAO;EACP,MAAM,MAAM;EACZ,IAAI,KAAK,eAAe;EACxB,MAAM,OAAO,SAAS,KAAK,YAAY,IAAI,SAAS,KAAK,KAAK,CAAC;EAC/D,IAAI,KAAK,oBAAoB;EAC7B,MAAM,OAAO,SAAS,KAAK,UAAU,CAAC;EACtC,IAAI,KAAK,gBAAgB;EACzB,MAAM,OAAO,KAAK,CAAC;EACnB,GAAG;EACH,EAAE,OAAO,KAAK,CAAC;EACf,CAAC;EACD;AACA;AACA;EACA,SAAS,yBAAyB,CAAC,KAAK,EAAE,SAAS,EAAE;EACrD,EAAEA,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;EAC5B,EAAEA,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC;AACjC;EACA,EAAEA,IAAI,IAAI,CAAC;EACX,EAAE,QAAQ,SAAS;EACnB,IAAI,KAAK,YAAY;EACrB,MAAM,IAAI,GAAG,MAAM,IAAI,MAAM,CAAC,CAAC,MAAM;EACrC,IAAI,KAAK,KAAK;EACd,MAAM,IAAI,GAAG,MAAM,IAAI,MAAM,CAAC,CAAC,MAAM;EACrC,IAAI,KAAK,GAAG;EACZ,MAAM,IAAI,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,MAAM;EACpC,IAAI,KAAK,UAAU;EACnB,MAAM,IAAI,KAAK,CAAC,SAAS,EAAE;EAC3B,QAAQ,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC;EACjC,OAAO,MAAM;EACb,QAAQ,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC;EACjC,OAAO;EACP,MAAM,MAAM;EACZ,GAAG;EACH,EAAE,OAAO,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EACxB,CAAC;;ECrGc,IAAM,UAAU,GAa7B,mBAAW,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE;AAC7D;EACA;EACA;EACA;EACA;EACA,EAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB;EACA;EACA;EACA;EACA,EAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB;EACA;EACA;EACA;EACA;EACA,EAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC;EACA;EACA;EACA;EACA;EACA,EAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/B;EACA;EACA;EACA;EACA;EACA,EAAI,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI,MAAM,CAAC;AACnC;AACA;EACA;EACA;EACA;EACA;EACA,EAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB;AACA;EACA;EACA;EACA;EACA,EAAI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAC5B;EACA;EACA;EACA;EACA;EACA,EAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC7B;EACA;EACA;EACA;EACA;EACA,EAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;AAC9B;EACA;AACA;EACA;EACA;EACA;EACA,EAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AACvB;EACA;EACA;EACA;EACA,EAAI,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;AAC9B;EACA,EAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;EAC7B;;kEAAC;AACH;AACA;EACE;EACF;EACA;EACA;uBACE,4BAAS,CAAC,EAAE;EACd,EAAID,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;EACtD,EAAI,OAAO,IAAI,CAAC,IAAI;EACpB,MAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EAC7E;EACA,MAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAC9E;EACE,EAAC;AACH;AACA;EACE;EACF;EACA;EACA;uBACE,4BAAS,CAAC,EAAE;EACd,EAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;EAC1B,EAAC;AACH;AACA;EACE;EACF;EACA;uBACE,oCAAc;EAChB,EAAI,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EACpD,EAAC;AACH;AACA;EACE;EACF;EACA;EACA;AACEE,uBAAI,2BAAW;EACjB,EAAI,OAAO,IAAI,CAAC,gBAAgB,KAAK,CAAC,CAAC;EACrC,EAAC;AACH;AACA;uBACE,0BAAS;EACX,EAAIF,IAAM,IAAI,GAAG,IAAI,UAAU;EAC/B,IAAM,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACzE;EACA,EAAI,IAAI,CAAC,SAAS,QAAU,IAAI,CAAC,SAAS,CAAC;EAC3C,EAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;EAClD,EAAI,IAAI,CAAC,YAAY,KAAO,IAAI,CAAC,YAAY,CAAC;EAC9C,EAAI,IAAI,CAAC,cAAc,GAAK,IAAI,CAAC,cAAc,CAAC;EAChD,EAAI,IAAI,CAAC,KAAK,YAAc,IAAI,CAAC,KAAK,CAAC;EACvC,EAAI,IAAI,CAAC,UAAU,OAAS,IAAI,CAAC,UAAU,CAAC;AAC5C;EACA,EAAI,OAAO,IAAI,CAAC;EACd;;;;EC5Ia,SAAS,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE;EACvC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE;EACvB,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE;EACzB,MAAM,OAAO,IAAI,CAAC;EAClB,KAAK,MAAM;EACX,MAAM,OAAO,KAAK,CAAC;EACnB,KAAK;EACL,GAAG;EACH,EAAE,OAAO,KAAK,CAAC;EACf,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EClBOA,IAAM,OAAO,GAAG,sBAAsB,CAAC;AAC9C,EAAOA,IAAM,QAAQ,GAAG,SAAS,CAAC;AAClC,EAAOA,IAAM,cAAc,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,IAAI,OAAO,CAAC;AAC1D;EACA;AACA,EAAO,SAAS,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE;EACzC,IAAIC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC;EAC3B,IAAIA,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,IAAIA,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,IAAIA,IAAI,MAAM,GAAG,CAAC,CAAC;EACnB,IAAIA,IAAI,MAAM,GAAG,CAAC,CAAC;EACnB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE;EAC1C,QAAQ,CAAC,GAAG,IAAI,CAAC;EACjB,QAAQ,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;EAC3B,KAAK,MAAM;EACX,QAAQ,CAAC,GAAG,IAAI,CAAC;EACjB,QAAQ,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;EAC3B,KAAK;EACL,IAAIA,IAAI,MAAM,GAAG,CAAC,CAAC;EACnB,IAAI,IAAI,MAAM,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,EAAE;EACxC,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE;EAC9C,YAAY,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;EAC5B,YAAY,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC;EACnC,YAAY,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;EAC/B,SAAS,MAAM;EACf,YAAY,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;EAC5B,YAAY,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC;EACnC,YAAY,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;EAC/B,SAAS;EACT,QAAQ,CAAC,GAAG,IAAI,CAAC;EACjB,QAAQ,IAAI,EAAE,KAAK,CAAC,EAAE;EACtB,YAAY,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC;EAC7B,SAAS;EACT,QAAQ,OAAO,MAAM,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,EAAE;EAC/C,YAAY,IAAI,CAAC,IAAI,GAAG,IAAI,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE;EAClD,gBAAgB,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;EAChC,gBAAgB,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;EACjC,gBAAgB,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC;EACzD,gBAAgB,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;EACnC,aAAa,MAAM;EACnB,gBAAgB,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;EAChC,gBAAgB,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;EACjC,gBAAgB,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC;EACzD,gBAAgB,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;EACnC,aAAa;EACb,YAAY,CAAC,GAAG,IAAI,CAAC;EACrB,YAAY,IAAI,EAAE,KAAK,CAAC,EAAE;EAC1B,gBAAgB,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC;EACjC,aAAa;EACb,SAAS;EACT,KAAK;EACL,IAAI,OAAO,MAAM,GAAG,IAAI,EAAE;EAC1B,QAAQ,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;EACxB,QAAQ,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;EACzB,QAAQ,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC;EACjD,QAAQ,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;EAC3B,QAAQ,CAAC,GAAG,IAAI,CAAC;EACjB,QAAQ,IAAI,EAAE,KAAK,CAAC,EAAE;EACtB,YAAY,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC;EAC7B,SAAS;EACT,KAAK;EACL,IAAI,OAAO,MAAM,GAAG,IAAI,EAAE;EAC1B,QAAQ,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;EACxB,QAAQ,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;EACzB,QAAQ,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC;EACjD,QAAQ,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;EAC3B,QAAQ,CAAC,GAAG,IAAI,CAAC;EACjB,QAAQ,IAAI,EAAE,KAAK,CAAC,EAAE;EACtB,YAAY,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC;EAC7B,SAAS;EACT,KAAK;EACL,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,EAAE;EACjC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC;EACxB,KAAK;EACL,IAAI,OAAO,MAAM,CAAC;EAClB,CAAC;AACD,AAoDA;AACA,EAAO,SAAS,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE;EAClC,IAAIA,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,IAAI,KAAKA,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,IAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAC;EAC7C,IAAI,OAAO,CAAC,CAAC;EACb,CAAC;AACD;AACA,EAAO,SAAS,GAAG,CAAC,CAAC,EAAE;EACvB,IAAI,OAAO,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;EAC/B,CAAC;;ECvIDD,IAAM,YAAY,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,OAAO,IAAI,OAAO,CAAC;EAClDA,IAAM,YAAY,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,OAAO,IAAI,OAAO,CAAC;EAClDA,IAAM,YAAY,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,OAAO,IAAI,OAAO,GAAG,OAAO,CAAC;AAC5D;EACAA,IAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EACjBA,IAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EAClBA,IAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;EACnBA,IAAM,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;EAClBA,IAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACjB;EACA,SAAS,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;EACvD,IAAIC,IAAI,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EAC3C,IAAIA,IAAI,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACrE;EACA,IAAID,IAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;EACxB,IAAIA,IAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;EACxB,IAAIA,IAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;EACxB,IAAIA,IAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;AACxB;EACA,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;EACnB,IAAI,CAAC,GAAG,QAAQ,GAAG,GAAG,CAAC;EACvB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;EACxB,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACpB,IAAI,CAAC,GAAG,QAAQ,GAAG,GAAG,CAAC;EACvB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;EACxB,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACpB,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;EAC9D,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;EACnB,IAAI,CAAC,GAAG,QAAQ,GAAG,GAAG,CAAC;EACvB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;EACxB,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACpB,IAAI,CAAC,GAAG,QAAQ,GAAG,GAAG,CAAC;EACvB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;EACxB,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACpB,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;EAC9D,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACjB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACpB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC;EAC5C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACjB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACpB,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,CAAC;EAC1C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACjB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACpB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC;EAC5C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACjB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACpB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,CAAC;EAC5C,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AACd;EACA,IAAIC,IAAI,GAAG,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC7B,IAAIA,IAAI,QAAQ,GAAG,YAAY,GAAG,MAAM,CAAC;EACzC,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAI,CAAC,GAAG,IAAI,QAAQ,EAAE;EAC7C,QAAQ,OAAO,GAAG,CAAC;EACnB,KAAK;AACL;EACA,IAAI,KAAK,GAAG,EAAE,GAAG,GAAG,CAAC;EACrB,IAAI,OAAO,GAAG,EAAE,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC;EAChD,IAAI,KAAK,GAAG,EAAE,GAAG,GAAG,CAAC;EACrB,IAAI,OAAO,GAAG,EAAE,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC;EAChD,IAAI,KAAK,GAAG,EAAE,GAAG,GAAG,CAAC;EACrB,IAAI,OAAO,GAAG,EAAE,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC;EAChD,IAAI,KAAK,GAAG,EAAE,GAAG,GAAG,CAAC;EACrB,IAAI,OAAO,GAAG,EAAE,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC;AAChD;EACA,IAAI,IAAI,OAAO,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,EAAE;EAC1E,QAAQ,OAAO,GAAG,CAAC;EACnB,KAAK;AACL;EACA,IAAI,QAAQ,GAAG,YAAY,GAAG,MAAM,GAAG,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACtE,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,OAAO,GAAG,GAAG,GAAG,OAAO,KAAK,GAAG,GAAG,OAAO,GAAG,GAAG,GAAG,OAAO,CAAC,CAAC;EAC7E,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAI,CAAC,GAAG,IAAI,QAAQ,IAAE,OAAO,GAAG,GAAC;AACxD;EACA,IAAI,EAAE,GAAG,OAAO,GAAG,GAAG,CAAC;EACvB,IAAI,CAAC,GAAG,QAAQ,GAAG,OAAO,CAAC;EAC3B,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;EAC5B,IAAI,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC;EACxB,IAAI,CAAC,GAAG,QAAQ,GAAG,GAAG,CAAC;EACvB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;EACxB,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACpB,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;EAC9D,IAAI,EAAE,GAAG,OAAO,GAAG,GAAG,CAAC;EACvB,IAAI,CAAC,GAAG,QAAQ,GAAG,OAAO,CAAC;EAC3B,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;EAC5B,IAAI,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC;EACxB,IAAI,CAAC,GAAG,QAAQ,GAAG,GAAG,CAAC;EACvB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;EACxB,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACpB,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;EAC9D,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACjB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACpB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC;EAC5C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACjB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACpB,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,CAAC;EAC1C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACjB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACpB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC;EAC5C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACjB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACpB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,CAAC;EAC5C,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;EACd,IAAID,IAAM,KAAK,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;AACtC;EACA,IAAI,EAAE,GAAG,GAAG,GAAG,OAAO,CAAC;EACvB,IAAI,CAAC,GAAG,QAAQ,GAAG,GAAG,CAAC;EACvB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;EACxB,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACpB,IAAI,CAAC,GAAG,QAAQ,GAAG,OAAO,CAAC;EAC3B,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;EAC5B,IAAI,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC;EACxB,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;EAC9D,IAAI,EAAE,GAAG,GAAG,GAAG,OAAO,CAAC;EACvB,IAAI,CAAC,GAAG,QAAQ,GAAG,GAAG,CAAC;EACvB,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;EACxB,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACpB,IAAI,CAAC,GAAG,QAAQ,GAAG,OAAO,CAAC;EAC3B,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;EAC5B,IAAI,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC;EACxB,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;EAC9D,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACjB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACpB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC;EAC5C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACjB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACpB,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,CAAC;EAC1C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACjB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACpB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC;EAC5C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACjB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACpB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,CAAC;EAC5C,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;EACd,IAAIA,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;AAC3C;EACA,IAAI,EAAE,GAAG,OAAO,GAAG,OAAO,CAAC;EAC3B,IAAI,CAAC,GAAG,QAAQ,GAAG,OAAO,CAAC;EAC3B,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;EAC5B,IAAI,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC;EACxB,IAAI,CAAC,GAAG,QAAQ,GAAG,OAAO,CAAC;EAC3B,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;EAC5B,IAAI,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC;EACxB,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;EAC9D,IAAI,EAAE,GAAG,OAAO,GAAG,OAAO,CAAC;EAC3B,IAAI,CAAC,GAAG,QAAQ,GAAG,OAAO,CAAC;EAC3B,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;EAC5B,IAAI,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC;EACxB,IAAI,CAAC,GAAG,QAAQ,GAAG,OAAO,CAAC;EAC3B,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;EAC5B,IAAI,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC;EACxB,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;EAC9D,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACjB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACpB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC;EAC5C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACjB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACpB,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,CAAC;EAC1C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACjB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACpB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC;EAC5C,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACjB,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACpB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,CAAC;EAC5C,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;EACd,IAAIA,IAAM,IAAI,GAAG,GAAG,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACzC;EACA,IAAI,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;EACvB,CAAC;AACD;AACA,EAAO,SAAS,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;EACjD,IAAIA,IAAM,OAAO,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;EAC1C,IAAIA,IAAM,QAAQ,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;EAC3C,IAAIA,IAAM,GAAG,GAAG,OAAO,GAAG,QAAQ,CAAC;AACnC;EACA,IAAI,IAAI,OAAO,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,OAAO,QAAQ,GAAG,CAAC,CAAC,IAAE,OAAO,GAAG,GAAC;AACxF;EACA,IAAIA,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC,CAAC;EAChD,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,YAAY,GAAG,MAAM,IAAE,OAAO,GAAG,GAAC;AAC3D;EACA,IAAI,OAAO,CAAC,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;EAC1D,CAAC;;ECnLD;EACA;EACA;EACA;EACA;EACA;EACA;AACA,EAAe,SAAS,UAAU,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;EAC/C,EAAEA,IAAM,GAAG,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjE,EAAE,IAAI,GAAG,GAAG,CAAC,IAAE,OAAO,CAAC,CAAC,GAAC;EACzB,EAAE,IAAI,GAAG,GAAG,CAAC,IAAE,OAAO,CAAC,GAAC;EACxB,EAAE,OAAO,CAAC,CAAC;EACX,CAAC;;ECZD;EACA;EACA;EACA;EACA;AACA,EAAe,SAAS,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE;EAC9C,EAAEA,IAAM,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC;EACtB,EAAEA,IAAM,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC;AACtB;EACA;EACA,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAE,OAAO,CAAC,GAAC;EAC9B,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAE,OAAO,CAAC,CAAC,GAAC;AAC/B;EACA;EACA;EACA,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAE,OAAO,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAC;AACrD;EACA,EAAE,OAAO,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,AAAI,CAAC,CAAC;EACtC,CAAC;AACD;AACA;EACA;EACA,SAAS,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;EACtC;EACA;EACA,EAAE,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI;EACzB,MAAI,OAAO,EAAE,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,GAAC;AAC5B;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,IAAI,UAAU,CAAC,EAAE,EAAE,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;EACtE;EACA,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;EACvD,GAAG;AACH;EACA,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,SAAS,IAAI,EAAE,CAAC,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;EAClD,CAAC;EACD;;ECtCA;EACA;EACA;EACA;EACA;EACA;AACA,EAAe,SAAS,aAAa,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,GAAG;EACrD,EAAEA,IAAM,CAAC,GAAG,IAAI,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,aAAa,EAAE,CAAC,SAAS,CAAC,CAAC;EAClE,EAAEA,IAAM,CAAC,GAAG,IAAI,UAAU,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;AAClE;EACA;EACA,EAAE,IAAI,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;EAC7C,IAAI,OAAO,CAAC,IAAI,CAAC,oCAAoC,EAAE,EAAE,CAAC,CAAC;EAC3D,GAAG;EACH;AACA;EACA,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,SAAS,CAAC;AAC3C;EACA;EACA,EAAE,IAAI,aAAa,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;EAC3C,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;EAC9B,IAAI,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC;EACnB,GAAG;AACH;EACA;EACA;AACA;EACA,EAAE,EAAE,CAAC,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC;EAC/B,EAAE,EAAE,CAAC,UAAU,GAAG,CAAC,CAAC;AACpB;EACA,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAChB,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAChB;EACA,EAAE,OAAO,KAAK,CAAC;EACf,CAAC;;ECtCD;AACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE;EAC5B,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvC,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE;EAC1B,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvC,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA,EAAe,uBAAU,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,eAAe,EAAE;EAC1D;EACA;EACA;EACA;EACA;EACA;EACA,EAAEA,IAAM,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C,EAAEA,IAAM,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C;AACA;EACA;AACA;EACA,EAAE,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EAC5B,IAAI,OAAO;EACX,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACrB,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACrB,KAAK,CAAC;EACN,GAAG;AACH;EACA;AACA;EACA;EACA,EAAEA,IAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3C,EAAEC,IAAI,KAAK,MAAM,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EACtC,EAAEA,IAAI,QAAQ,GAAG,KAAK,GAAG,KAAK,CAAC;EAC/B,EAAED,IAAM,OAAO,IAAI,UAAU,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EACtC;AACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,IAAI,QAAQ,GAAG,CAAC,8BAA8B;EAChD;EACA;EACA;EACA,IAAIA,IAAM,CAAC,GAAG,YAAY,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;EAC1C,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;EACxB;EACA,MAAM,OAAO,IAAI,CAAC;EAClB,KAAK;EACL,IAAIA,IAAM,CAAC,GAAG,YAAY,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;EAC1C,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;EACxB;EACA,MAAM,OAAO,IAAI,CAAC;EAClB,KAAK;EACL,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;EAC5B;EACA,MAAM,OAAO,eAAe,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAC3D,KAAK;EACL,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;EAC5B;EACA,MAAM,OAAO,eAAe,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAC3D,KAAK;EACL,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAChC,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,KAAK,GAAG,YAAY,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EAC9B,EAAE,QAAQ,GAAG,KAAK,GAAG,KAAK,CAAC;AAC3B;EACA,EAAE,IAAI,QAAQ,GAAG,CAAC,8BAA8B;EAChD;EACA,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;AACH;EACA,EAAEA,IAAM,EAAE,GAAG,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC;EACzC,EAAEA,IAAM,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,OAAO,CAAC;EAC/C,EAAEA,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EAChC,EAAEA,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC;EACA;EACA;EACA,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE;AAC9B;EACA;EACA,IAAI,IAAI,IAAI,KAAK,CAAC,EAAE;EACpB,MAAM,OAAO,eAAe,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAC7E,KAAK;AACL;EACA,IAAI,IAAI,IAAI,KAAK,CAAC,EAAE;EACpB,MAAM,OAAO,eAAe,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAC7E,KAAK;AACL;EACA,IAAI,IAAI,eAAe,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAE,OAAO,IAAI,GAAC;AACjE;EACA;EACA,IAAI,OAAO;EACX,MAAM,OAAO,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC;EAC1C,MAAM,OAAO,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC;EAC1C,KAAK,CAAC;EACN,GAAG;AACH;EACA,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;;EC1ID;EACA;EACA;EACA;EACA;EACA;AACA,EAAe,SAAS,oBAAoB,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;EAC/D;EACA;EACA;EACA;EACA,EAAEA,IAAM,KAAK,GAAG,YAAY;EAC5B,IAAI,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,KAAK;EACnC,IAAI,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,KAAK;EACnC,GAAG,CAAC;AACJ;EACA,EAAEA,IAAM,cAAc,GAAG,KAAK,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;EAClD,EAAE,IAAI,cAAc,KAAK,CAAC,IAAE,OAAO,CAAC,GAAC;AACrC;EACA;EACA,EAAE,IAAI,CAAC,cAAc,KAAK,CAAC;EAC3B,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC;EACnC,OAAO,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;EAC5D,IAAI,OAAO,CAAC,CAAC;EACb,GAAG;AACH;EACA,EAAE,IAAI,cAAc,KAAK,CAAC,IAAI,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,SAAS,EAAE;EAC/D;EACA;EACA;EACA;EACA;EACA,IAAI,OAAO,CAAC,CAAC;EACb,GAAG;AACH;EACA;EACA,EAAE,IAAI,cAAc,KAAK,CAAC,EAAE;AAC5B;EACA;EACA,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;EACjF,MAAM,aAAa,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;EAC1C,KAAK;AACL;EACA;EACA,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;EACjF,MAAM,aAAa,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;EAC1C,KAAK;EACL,IAAI,OAAO,CAAC,CAAC;EACb,GAAG;AACH;EACA;EACA,EAAEA,IAAM,MAAM,UAAU,EAAE,CAAC;EAC3B,EAAEC,IAAI,YAAY,IAAI,KAAK,CAAC;EAC5B,EAAEA,IAAI,aAAa,GAAG,KAAK,CAAC;AAC5B;EACA,EAAE,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE;EACpC,IAAI,YAAY,GAAG,IAAI,CAAC;EACxB,GAAG,MAAM,IAAI,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE;EAC5C,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;EAC1B,GAAG,MAAM;EACT,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;EAC1B,GAAG;AACH;EACA,EAAE,IAAI,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;EAC1D,IAAI,aAAa,GAAG,IAAI,CAAC;EACzB,GAAG,MAAM,IAAI,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;EAClE,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;EAChD,GAAG,MAAM;EACT,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;EAChD,GAAG;AACH;EACA,EAAE,IAAI,CAAC,YAAY,IAAI,aAAa,KAAK,YAAY,EAAE;EACvD;EACA,IAAI,GAAG,CAAC,IAAI,GAAG,gBAAgB,CAAC;EAChC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK;EACvC,QAAQ,eAAe,GAAG,oBAAoB,CAAC;AAC/C;EACA,IAAI,IAAI,YAAY,IAAI,CAAC,aAAa,EAAE;EACxC;EACA;EACA,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;EAClE,KAAK;EACL,IAAI,OAAO,CAAC,CAAC;EACb,GAAG;AACH;EACA;EACA,EAAE,IAAI,aAAa,EAAE;EACrB,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;EACrD,IAAI,OAAO,CAAC,CAAC;EACb,GAAG;AACH;EACA;EACA,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE;EAC1C,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;EACrD,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;EACrD,IAAI,OAAO,CAAC,CAAC;EACb,GAAG;AACH;EACA;EACA,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;EACnD,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC9D;EACA,EAAE,OAAO,CAAC,CAAC;EACX,CAAC;;EC5GD;EACA;EACA;EACA;EACA;AACA,EAAe,SAAS,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE;EAClD,EAAE,IAAI,GAAG,KAAK,GAAG,IAAE,OAAO,CAAC,GAAC;AAC5B;EACA;EACA,EAAE,IAAI,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;EAClE,IAAI,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AAC7E;EACA;EACA,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,IAAE,OAAO,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAC;AACxF;EACA;EACA,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAE,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAC;AACnF;EACA;EACA;EACA,IAAI,IAAI,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,IAAE,OAAO,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAC;AAC9E;EACA;EACA;EACA,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EAC3C,GAAG;AACH;EACA,EAAE,IAAI,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,SAAS,EAAE;EACvC,IAAIA,IAAI,EAAE,GAAG,GAAG,CAAC,KAAK,EAAE,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;EACvC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,kCAAkC;EAC5E,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC;EAC3D,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAE,OAAO,CAAC,GAAC;EACvD,aAAW,OAAO,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,GAAC;EACzD,KAAK;EACL,GAAG,MAAM;EACT,IAAI,OAAO,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EAClC,GAAG;AACH;EACA,EAAE,OAAO,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EAChD,CAAC;;EClCc,SAAS,SAAS,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE;EAC1F,EAAED,IAAM,SAAS,GAAG,IAAIG,SAAI,CAAC,eAAe,CAAC,CAAC;EAC9C,EAAEH,IAAM,YAAY,GAAG,EAAE,CAAC;AAC1B;EACA,EAAEA,IAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD;EACA,EAAEC,IAAI,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;AACxB;EACA,EAAE,OAAO,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;EAClC,IAAIA,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC;EACjC,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7B;EACA;EACA,IAAI,IAAI,CAAC,SAAS,KAAK,YAAY,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU;EAClE,SAAS,SAAS,KAAK,UAAU,MAAM,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;EACnE,MAAM,MAAM;EACZ,KAAK;AACL;EACA,IAAI,IAAI,KAAK,CAAC,IAAI,EAAE;EACpB,MAAM,IAAI,IAAI,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;EAC7C,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;AAClC;EACA,MAAM,IAAI,IAAI,KAAK,KAAK,IAAE,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAC;EACtD,4BAA0B,IAAI,GAAG,IAAI,GAAC;AACtC;EACA,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClC;EACA,MAAMD,IAAM,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;EAC/C,MAAMC,IAAI,wBAAa,CAAC;EACxB,MAAM,aAAa,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EACjD,MAAM,IAAI,IAAI,EAAE;EAChB,QAAQ,IAAI,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC,EAAE;EACrE,UAAU,aAAa,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EACrD,UAAU,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;EACpD,SAAS;EACT,OAAO;AACP;EACA,MAAM,IAAI,IAAI,EAAE;EAChB,QAAQ,IAAI,oBAAoB,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,EAAE;EACrE,UAAUA,IAAI,QAAQ,GAAG,IAAI,CAAC;EAC9B,UAAU,IAAI,QAAQ,KAAK,KAAK,IAAE,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAC;EACtE,oCAAkC,QAAQ,GAAG,IAAI,GAAC;AAClD;EACA,UAAU,aAAa,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC;EACzD,UAAU,aAAa,CAAC,SAAS,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;EAC7D,UAAU,aAAa,CAAC,KAAK,MAAM,SAAS,MAAM,SAAS,CAAC,CAAC;EAC7D,SAAS;EACT,OAAO;EACP,KAAK,MAAM;EACX,MAAM,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC;EAC/B,MAAM,IAAI,GAAG,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1C;EACA,MAAM,IAAI,IAAI,IAAI,IAAI,EAAE;AACxB;EACA,QAAQ,IAAI,IAAI,KAAK,KAAK,IAAE,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAC;EACxD,8BAA4B,IAAI,GAAG,IAAI,GAAC;AACxC;EACA,QAAQ,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACpC,QAAQ,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAChC;EACA,QAAQ,IAAI,IAAI,IAAI,IAAI,EAAE;EAC1B,UAAU,oBAAoB,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;EAC/D,SAAS;EACT,OAAO;EACP,KAAK;EACL,GAAG;EACH,EAAE,OAAO,YAAY,CAAC;EACtB,CAAC;;EC7Ec,IAAM,OAAO,GAO1B,gBAAW,GAAG;EAChB,EAAI,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;EACrB,EAAI,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;EACtB,EAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;EACvB,EAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;EACpB,EAAC;AACH;oBACE,oCAAa;EACf,EAAI,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;EAC7B;;ECbF;EACA;EACA;EACA;EACA,SAAS,WAAW,CAAC,YAAY,EAAE;EACnC,EAAEA,IAAI,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;EACzB,EAAED,IAAM,YAAY,GAAG,EAAE,CAAC;EAC1B,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EACvD,IAAI,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;EAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ;EACrC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;EAClD,MAAM,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAC/B,KAAK;EACL,GAAG;EACH;EACA,EAAEC,IAAI,MAAM,GAAG,KAAK,CAAC;EACrB,EAAE,OAAO,CAAC,MAAM,EAAE;EAClB,IAAI,MAAM,GAAG,IAAI,CAAC;EAClB,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EACzD,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG;EACvB,QAAQ,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;EACnE,QAAQ,GAAG,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;EAC9B,QAAQ,YAAY,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC9C,QAAQ,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;EAClC,QAAQ,MAAM,GAAG,KAAK,CAAC;EACvB,OAAO;EACP,KAAK;EACL,GAAG;AACH;AACA;EACA,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EACvD,IAAI,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;EAC5B,IAAI,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;EACvB,GAAG;AACH;EACA;EACA;EACA,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EACvD,IAAI,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;EAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;EACrB,MAAM,GAAG,GAAG,KAAK,CAAC,QAAQ,CAAC;EAC3B,MAAM,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC;EACjD,MAAM,KAAK,CAAC,UAAU,CAAC,QAAQ,GAAG,GAAG,CAAC;EACtC,KAAK;EACL,GAAG;AACH;EACA,EAAE,OAAO,YAAY,CAAC;EACtB,CAAC;AACD;AACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,OAAO,CAAC,GAAG,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE;EACxD,EAAEA,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC;EACtB,IAAI,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK;EAC/B,IAAI,EAAE,CAAC;EACP,EAAED,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;AACrC;EACA,EAAE,IAAI,MAAM,GAAG,MAAM;EACrB,MAAI,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,GAAC;AACpC;EACA,EAAE,OAAO,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;EAC9D,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;EAC5B,MAAM,OAAO,MAAM,CAAC;EACpB,KAAK,MAAM;EACX,MAAM,MAAM,EAAE,CAAC;EACf,KAAK;EACL,IAAI,IAAI,MAAM,GAAG,MAAM,EAAE;EACzB,MAAM,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC;EACtC,KAAK;EACL,GAAG;AACH;EACA,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC;AACnB;EACA,EAAE,OAAO,SAAS,CAAC,MAAM,CAAC,IAAI,MAAM,GAAG,OAAO,EAAE;EAChD,IAAI,MAAM,EAAE,CAAC;EACb,GAAG;AACH;EACA,EAAE,OAAO,MAAM,CAAC;EAChB,CAAC;AACD;AACA;EACA,SAAS,4BAA4B,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE;EAClE,EAAEA,IAAM,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC;EAChC,EAAE,IAAI,KAAK,CAAC,YAAY,IAAI,IAAI,EAAE;EAClC,IAAIA,IAAM,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;EAC5C;EACA;EACA;EACA;EACA,IAAIA,IAAM,cAAc,GAAG,YAAY,CAAC,eAAe,CAAC;EACxD,IAAIA,IAAM,qBAAqB,GAAG,YAAY,CAAC,gBAAgB,CAAC;EAChE,IAAI,IAAI,qBAAqB,GAAG,CAAC,EAAE;EACnC;EACA;EACA,MAAMA,IAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC;EACpD,MAAM,IAAI,YAAY,CAAC,MAAM,IAAI,IAAI,EAAE;EACvC;EACA;EACA,QAAQA,IAAM,eAAe,GAAG,YAAY,CAAC,MAAM,CAAC;EACpD,QAAQ,QAAQ,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;EAC1D,QAAQ,OAAO,CAAC,MAAM,GAAG,eAAe,CAAC;EACzC,QAAQ,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC;EACvD,OAAO,MAAM;EACb;EACA;EACA,QAAQ,QAAQ,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;EACzD,QAAQ,OAAO,CAAC,MAAM,GAAG,cAAc,CAAC;EACxC,QAAQ,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;EAC3D,OAAO;EACP,KAAK,MAAM;EACX;EACA,MAAM,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;EAC5B,MAAM,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC;EACrD,KAAK;EACL,GAAG,MAAM;EACT;EACA,IAAI,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;EAC1B,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;EACtB,GAAG;EACH,EAAE,OAAO,OAAO,CAAC;EACjB,CAAC;AACD;EACA;EACA;EACA;EACA;AACA,EAAe,SAAS,YAAY,CAAC,YAAY,EAAE;EACnD,EAAEC,IAAI,CAAC,EAAE,GAAG,CAAC;EACb,EAAED,IAAM,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC;AACjD;EACA;EACA,EAAEA,IAAM,SAAS,GAAG,EAAE,CAAC;EACvB,EAAEA,IAAM,QAAQ,GAAG,EAAE,CAAC;AACtB;EACA,2BAAuD;AACvD;EACA,IAAI,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE;EACtB,MAAM,OAAS;EACf,KAAK;AACL;EACA,IAAIA,IAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC;EACtC,IAAIA,IAAM,OAAO,GAAG,4BAA4B,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;AACvF;EACA;EACA,IAAIA,IAAM,eAAe,aAAI,GAAG,EAAK;EACrC,MAAM,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;EAC5B,MAAM,IAAI,GAAG,GAAG,YAAY,CAAC,MAAM,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE;EAC1D,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,eAAe,GAAG,SAAS,CAAC;EACtD,OAAO;EACP,KAAK,CAAC;AACN;EACA,IAAIC,IAAI,GAAG,GAAG,CAAC,CAAC;EAChB,IAAIA,IAAI,OAAO,GAAG,CAAC,CAAC;AACpB;EACA,IAAID,IAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;EAC1C,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACjC;EACA;EACA,IAAI,OAAO,IAAI,EAAE;EACjB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAC;AAC3B;EACA,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC;AACvC;EACA,MAAM,eAAe,CAAC,GAAG,CAAC,CAAC;EAC3B,MAAM,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;AACnD;EACA,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;AAC3D;EACA,MAAM,IAAI,GAAG,IAAI,OAAO,IAAI,GAAG,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE;EAC9E,QAAQ,MAAM;EACd,OAAO;EACP,KAAK;AACL;EACA,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EAC3B;;IAxCE,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,WAwClD;AACH;EACA,EAAE,OAAO,QAAQ,CAAC;EAClB,CAAC;;ECvLD,aAAc,GAAG,SAAS,CAAC;EAC3B,aAAsB,GAAG,SAAS,CAAC;AACnC;EACA,SAAS,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE;EAClC,IAAI,IAAI,EAAE,IAAI,YAAY,SAAS,CAAC,IAAE,OAAO,IAAI,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,GAAC;AAC1E;EACA,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;EAC3B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;EACnC,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,cAAc,CAAC;AAC7C;EACA,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;EACzB,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAC;EACxE,KAAK;EACL,CAAC;AACD;EACA,SAAS,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE;EAC9B,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACtC,CAAC;AACD;EACA,SAAS,CAAC,SAAS,GAAG;AACtB;EACA,IAAI,IAAI,EAAE,UAAU,IAAI,EAAE;EAC1B,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC7B,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;EACtB,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;EAClC,KAAK;AACL;EACA,IAAI,GAAG,EAAE,YAAY;EACrB,QAAQ,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAE,OAAO,SAAS,GAAC;AAChD;EACA,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC/B,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;AACtB;EACA,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;EAC7B,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EAClD,YAAY,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC1B,SAAS;EACT,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AACxB;EACA,QAAQ,OAAO,GAAG,CAAC;EACnB,KAAK;AACL;EACA,IAAI,IAAI,EAAE,YAAY;EACtB,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC5B,KAAK;AACL;EACA,IAAI,GAAG,EAAE,UAAU,GAAG,EAAE;EACxB,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;EAC7B,QAAQ,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;EACnC,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7B;EACA,QAAQ,OAAO,GAAG,GAAG,CAAC,EAAE;EACxB,YAAY,IAAI,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;EACxC,YAAY,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;EACvC,YAAY,IAAI,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,IAAE,QAAM;EACnD,YAAY,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;EAChC,YAAY,GAAG,GAAG,MAAM,CAAC;EACzB,SAAS;AACT;EACA,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;EACzB,KAAK;AACL;EACA,IAAI,KAAK,EAAE,UAAU,GAAG,EAAE;EAC1B,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;EAC7B,QAAQ,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;EACnC,QAAQ,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;EAC1C,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7B;EACA,QAAQ,OAAO,GAAG,GAAG,UAAU,EAAE;EACjC,YAAY,IAAI,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;EACtC,YAAY,IAAI,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;EACjC,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;AAClC;EACA,YAAY,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;EACvE,gBAAgB,IAAI,GAAG,KAAK,CAAC;EAC7B,gBAAgB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;EACnC,aAAa;EACb,YAAY,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAE,QAAM;AAChD;EACA,YAAY,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;EAC7B,YAAY,GAAG,GAAG,IAAI,CAAC;EACvB,SAAS;AACT;EACA,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;EACzB,KAAK;EACL,CAAC;;;EClFDA,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;EACrBA,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACrB;EACAC,IAAI,SAAS,GAAG,CAAC,CAAC;AAClB;AACA;EACA,SAAS,cAAc,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE;EAClF,EAAEA,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC7B,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAC5D,IAAI,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;EAC1B,IAAI,EAAE,GAAG,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC9B,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EACzD,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,SAAS,SAAS,CAAC,CAAC;EACzD,IAAI,EAAE,CAAC,UAAU,GAAG,EAAE,CAAC;AACvB;EACA,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE;EAC5C,MAAM,SAAS;EACf,KAAK;AACL;EACA,IAAI,EAAE,CAAC,SAAS,GAAG,EAAE,CAAC,SAAS,GAAG,KAAK,CAAC;EACxC,IAAI,IAAI,CAAC,cAAc,EAAE;EACzB,MAAM,EAAE,CAAC,cAAc,GAAG,KAAK,CAAC;EAChC,MAAM,EAAE,CAAC,cAAc,GAAG,KAAK,CAAC;EAChC,KAAK;EACL,IAAI,IAAI,aAAa,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE;EACnC,MAAM,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;EACrB,KAAK,MAAM;EACX,MAAM,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;EACrB,KAAK;AACL;EACA,IAAID,IAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;EAC/B,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9B,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9B,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9B,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9B;EACA;EACA;EACA,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EACf,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EACf,GAAG;EACH,CAAC;AACD;AACA;AACA,EAAe,SAAS,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE;EAC9E,EAAEA,IAAM,UAAU,GAAG,IAAII,SAAK,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;EACpD,EAAEH,IAAI,UAAU,EAAE,cAAc,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;AAC/C;EACA,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;EAChD,IAAI,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;EAC5B,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;EACrD,MAAM,cAAc,GAAG,CAAC,KAAK,CAAC,CAAC;EAC/B,MAAM,IAAI,cAAc,IAAE,SAAS,EAAE,GAAC;EACtC,MAAM,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;EACxF,KAAK;EACL,GAAG;AACH;EACA,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;EACjD,IAAI,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC7B,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;EACrD,MAAM,cAAc,GAAG,CAAC,KAAK,CAAC,CAAC;EAC/B,MAAM,IAAI,SAAS,KAAK,UAAU,IAAE,cAAc,GAAG,KAAK,GAAC;EAC3D,MAAM,IAAI,cAAc,IAAE,SAAS,EAAE,GAAC;EACtC,MAAM,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;EACzF,KAAK;EACL,GAAG;AACH;EACA,EAAE,OAAO,UAAU,CAAC;EACpB,CAAC;;EC/DDD,IAAM,KAAK,GAAG,EAAE,CAAC;AACjB;AACA;EACA,SAAS,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE;EACxD,EAAEC,IAAI,MAAM,GAAG,IAAI,CAAC;EACpB,EAAE,IAAI,OAAO,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;EAC9C,IAAI,WAAW,SAAS,KAAK,YAAY,EAAE;EAC3C,MAAM,MAAM,GAAG,KAAK,CAAC;EACrB,KAAK,MAAM,IAAI,SAAS,KAAK,UAAU,EAAE;EACzC,MAAM,MAAM,GAAG,OAAO,CAAC;EACvB,KAAK,MAAM,IAAI,SAAS,KAAK,KAAK;EAClC,eAAe,SAAS,KAAK,GAAG,EAAE;EAClC,MAAM,MAAM,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,GAAG,OAAO,CAAC;EAC3D,KAAK;EACL,GAAG;EACH,EAAE,OAAO,MAAM,CAAC;EAChB,CAAC;AACD;AACA;EACA,SAAS,aAAa,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE;EACnE,EAAEA,IAAI,MAAM,GAAG,IAAI,CAAC;EACpB,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;EACzB,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;EACzB,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;EACzB,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE;EAC3B,IAAI,WAAW,SAAS,KAAK,YAAY,EAAE;EAC3C,MAAM,MAAM,GAAG,KAAK,CAAC;EACrB,KAAK,MAAM,IAAI,SAAS,KAAK,UAAU,EAAE;EACzC,MAAM,MAAM,GAAG,OAAO,CAAC;EACvB,KAAK,MAAM,IAAI,SAAS,KAAK,KAAK;EAClC,eAAe,SAAS,KAAK,GAAG,EAAE;EAClC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;EACxC,KAAK;EACL,GAAG;EACH,EAAE,OAAO,MAAM,CAAC;EAChB,CAAC;AACD;AACA;AACA,EAAe,SAAS,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE;EAC9D,EAAE,IAAI,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;EAC5C,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC;EACxB,GAAG;EACH,EAAE,IAAI,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;EAC7C,IAAI,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC;EAC1B,GAAG;EACH,EAAEA,IAAI,OAAO,GAAG,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;EAC/D,EAAE,IAAI,OAAO,EAAE;EACf,IAAI,OAAO,OAAO,KAAK,KAAK,GAAG,IAAI,GAAG,OAAO,CAAC;EAC9C,GAAG;EACH,EAAED,IAAM,KAAK,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;EAC3D,EAAEA,IAAM,KAAK,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;AAC3D;EACA;EACA,EAAEA,IAAM,UAAU,GAAG,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;EAC3E;AACA;EACA,EAAE,OAAO,GAAG,aAAa,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;EACtE,EAAE,IAAI,OAAO,EAAE;EACf,IAAI,OAAO,OAAO,KAAK,KAAK,GAAG,IAAI,GAAG,OAAO,CAAC;EAC9C,GAAG;EACH;EACA,EAAEA,IAAM,YAAY,GAAGK,SAAiB,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;EACjG;AACA;EACA;EACA,EAAEL,IAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,AAAW,CAAC,CAAC;EACzD;AACA;EACA;EACA,EAAEA,IAAM,QAAQ,GAAG,EAAE,CAAC;EACtB,EAAE,KAAKC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC5C,IAAIA,IAAI,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9B,IAAI,IAAI,OAAO,CAAC,UAAU,EAAE,EAAE;EAC9B;EACA,MAAMA,IAAI,KAAK,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;EACnC;EACA,MAAM,KAAKA,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACvD,QAAQA,IAAI,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;EACxC,QAAQ,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC;EAC5C,OAAO;EACP,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAC3B,KAAK;EACL,GAAG;AACH;EACA,EAAE,OAAO,QAAQ,CAAC;EAClB,CAAC;;ECvFM,SAAS,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE;EAC1C,EAAE,OAAO,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;EAC3C,CAAC;AACD;AACA,EAAO,SAAS,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE;EACzC,EAAE,OAAO,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;EAChD,CAAC;AACD;AACA,EAAO,SAAS,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE;EACxC,EAAE,OAAO,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;EACzC,CAAC;AACD;AACA,EAAO,SAASK,cAAY,EAAE,OAAO,EAAE,QAAQ,EAAE;EACjD,EAAE,OAAO,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;EAClD,CAAC;AACD;EACA;EACA;EACA;AACA,AAAY,MAAC,UAAU,GAAG,SAAE,KAAK,cAAE,UAAU,gBAAE,YAAY,OAAE,GAAG,EAAE;;;;;;;;;;;;;;;;"}