# isows

## 1.0.7

### Patch Changes

- [#21](https://github.com/wevm/isows/pull/21) [`8d6f65858afc0cb131d40c6f415542347b0c909c`](https://github.com/wevm/isows/commit/8d6f65858afc0cb131d40c6f415542347b0c909c) Thanks [@lucasloisp](https://github.com/lucasloisp)! - Fixed `react-native` export in `package.json#exports`.

## 1.0.6

### Patch Changes

- [#15](https://github.com/wevm/isows/pull/15) [`a8d943b5c2a47ada792d9070379aacabb8d01ceb`](https://github.com/wevm/isows/commit/a8d943b5c2a47ada792d9070379aacabb8d01ceb) Thanks [@lkwr](https://github.com/lkwr)! - Renamed conditional export `worked` to `workerd` in `package.json#exports`.

## 1.0.5

### Patch Changes

- [`fc36fcbbea8a91411263dbd053e44623eac385b5`](https://github.com/wevm/isows/commit/fc36fcbbea8a91411263dbd053e44623eac385b5) Thanks [@jxom](https://github.com/jxom)! - Added `worked` export to `package.json#exports`.

## 1.0.4

### Patch Changes

- [#9](https://github.com/wevm/isows/pull/9) [`1d0e496db949a59ec6d3e635522f42b69daa52b7`](https://github.com/wevm/isows/commit/1d0e496db949a59ec6d3e635522f42b69daa52b7) Thanks [@iketw](https://github.com/iketw)! - Added `react-native` export to `package.json#exports`.

## 1.0.3

### Patch Changes

- [`8d37b99`](https://github.com/wagmi-dev/isows/commit/8d37b99ad08c286c20a50864d98c8119d7dae0db) Thanks [@jxom](https://github.com/jxom)! - Fixed CommonJS builds.

## 1.0.2

### Patch Changes

- [`c0c0e87`](https://github.com/wagmi-dev/isows/commit/c0c0e8724407a989da70bfff29cf444ccbf31b84) Thanks [@jxom](https://github.com/jxom)! - Removed `"module"` from `package.json`.

- [`59ccdf9`](https://github.com/wagmi-dev/isows/commit/59ccdf9a45900a5854b010f58bf4a6f11169c23f) Thanks [@jxom](https://github.com/jxom)! - Added `"deno"` conditional export.

## 1.0.0

### Major Changes

- [`bd4f010`](https://github.com/wagmi-dev/isows/commit/bd4f010d8267a0c48ecc9c09d3a5e8ff8aa1b05d) Thanks [@jxom](https://github.com/jxom)! - Initial release.
