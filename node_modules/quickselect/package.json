{"name": "quickselect", "version": "3.0.0", "type": "module", "description": "A tiny and fast selection algorithm in JavaScript.", "repository": "github:mourner/quickselect", "module": "index.js", "main": "index.js", "exports": "./index.js", "devDependencies": {"eslint": "^9.6.0", "eslint-config-mourner": "^4.0.1", "esm": "^3.2.25", "rollup": "^4.18.0", "tape": "^5.8.1", "typescript": "^5.5.3"}, "scripts": {"pretest": "eslint *.js && tsc", "test": "node test.js", "bench": "node bench.js"}, "files": ["index.js", "index.d.ts"], "types": "index.d.ts", "keywords": ["selection", "algorithm", "quickselect", "sort", "partial", "floyd", "rivest"], "author": "<PERSON>", "license": "ISC"}