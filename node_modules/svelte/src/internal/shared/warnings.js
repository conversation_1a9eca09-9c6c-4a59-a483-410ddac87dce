/* This file is generated by scripts/process-messages/index.js. Do not edit! */

import { DEV } from 'esm-env';

var bold = 'font-weight: bold';
var normal = 'font-weight: normal';

/**
 * `<svelte:element this="%tag%">` is a void element — it cannot have content
 * @param {string} tag
 */
export function dynamic_void_element_content(tag) {
	if (DEV) {
		console.warn(`%c[svelte] dynamic_void_element_content\n%c\`<svelte:element this="${tag}">\` is a void element — it cannot have content\nhttps://svelte.dev/e/dynamic_void_element_content`, bold, normal);
	} else {
		console.warn(`https://svelte.dev/e/dynamic_void_element_content`);
	}
}

/**
 * The following properties cannot be cloned with `$state.snapshot` — the return value contains the originals:
 * 
 * %properties%
 * @param {string | undefined | null} [properties]
 */
export function state_snapshot_uncloneable(properties) {
	if (DEV) {
		console.warn(
			`%c[svelte] state_snapshot_uncloneable\n%c${properties
				? `The following properties cannot be cloned with \`$state.snapshot\` — the return value contains the originals:

${properties}`
				: 'Value cannot be cloned with `$state.snapshot` — the original value was returned'}\nhttps://svelte.dev/e/state_snapshot_uncloneable`,
			bold,
			normal
		);
	} else {
		console.warn(`https://svelte.dev/e/state_snapshot_uncloneable`);
	}
}