{"version": 3, "sources": ["../../svelte/src/reactivity/reactive-value.js"], "sourcesContent": ["import { createSubscriber } from './create-subscriber.js';\n\n/**\n * @template T\n */\nexport class ReactiveValue {\n\t#fn;\n\t#subscribe;\n\n\t/**\n\t *\n\t * @param {() => T} fn\n\t * @param {(update: () => void) => void} onsubscribe\n\t */\n\tconstructor(fn, onsubscribe) {\n\t\tthis.#fn = fn;\n\t\tthis.#subscribe = createSubscriber(onsubscribe);\n\t}\n\n\tget current() {\n\t\tthis.#subscribe();\n\t\treturn this.#fn();\n\t}\n}\n"], "mappings": ";;;;;AAKO,IAAM,gBAAN,MAAoB;AAAA,EAC1B;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,IAAI,aAAa;AAC5B,SAAK,MAAM;AACX,SAAK,aAAa,iBAAiB,WAAW;AAAA,EAC/C;AAAA,EAEA,IAAI,UAAU;AACb,SAAK,WAAW;AAChB,WAAO,KAAK,IAAI;AAAA,EACjB;AACD;", "names": []}