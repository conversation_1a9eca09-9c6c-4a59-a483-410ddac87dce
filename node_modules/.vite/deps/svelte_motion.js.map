{"version": 3, "sources": ["../../svelte/src/motion/utils.js", "../../svelte/src/motion/spring.js", "../../svelte/src/motion/tweened.js", "../../svelte/src/motion/index.js"], "sourcesContent": ["/**\n * @param {any} obj\n * @returns {obj is Date}\n */\nexport function is_date(obj) {\n\treturn Object.prototype.toString.call(obj) === '[object Date]';\n}\n", "/** @import { Task } from '#client' */\n/** @import { SpringOpts, SpringUpdateOpts, TickContext } from './private.js' */\n/** @import { Spring as SpringStore } from './public.js' */\nimport { writable } from '../store/shared/index.js';\nimport { loop } from '../internal/client/loop.js';\nimport { raf } from '../internal/client/timing.js';\nimport { is_date } from './utils.js';\nimport { set, state } from '../internal/client/reactivity/sources.js';\nimport { render_effect } from '../internal/client/reactivity/effects.js';\nimport { tag } from '../internal/client/dev/tracing.js';\nimport { get } from '../internal/client/runtime.js';\nimport { deferred, noop } from '../internal/shared/utils.js';\nimport { DEV } from 'esm-env';\n\n/**\n * @template T\n * @param {TickContext} ctx\n * @param {T} last_value\n * @param {T} current_value\n * @param {T} target_value\n * @returns {T}\n */\nfunction tick_spring(ctx, last_value, current_value, target_value) {\n\tif (typeof current_value === 'number' || is_date(current_value)) {\n\t\t// @ts-ignore\n\t\tconst delta = target_value - current_value;\n\t\t// @ts-ignore\n\t\tconst velocity = (current_value - last_value) / (ctx.dt || 1 / 60); // guard div by 0\n\t\tconst spring = ctx.opts.stiffness * delta;\n\t\tconst damper = ctx.opts.damping * velocity;\n\t\tconst acceleration = (spring - damper) * ctx.inv_mass;\n\t\tconst d = (velocity + acceleration) * ctx.dt;\n\t\tif (Math.abs(d) < ctx.opts.precision && Math.abs(delta) < ctx.opts.precision) {\n\t\t\treturn target_value; // settled\n\t\t} else {\n\t\t\tctx.settled = false; // signal loop to keep ticking\n\t\t\t// @ts-ignore\n\t\t\treturn is_date(current_value) ? new Date(current_value.getTime() + d) : current_value + d;\n\t\t}\n\t} else if (Array.isArray(current_value)) {\n\t\t// @ts-ignore\n\t\treturn current_value.map((_, i) =>\n\t\t\t// @ts-ignore\n\t\t\ttick_spring(ctx, last_value[i], current_value[i], target_value[i])\n\t\t);\n\t} else if (typeof current_value === 'object') {\n\t\tconst next_value = {};\n\t\tfor (const k in current_value) {\n\t\t\t// @ts-ignore\n\t\t\tnext_value[k] = tick_spring(ctx, last_value[k], current_value[k], target_value[k]);\n\t\t}\n\t\t// @ts-ignore\n\t\treturn next_value;\n\t} else {\n\t\tthrow new Error(`Cannot spring ${typeof current_value} values`);\n\t}\n}\n\n/**\n * The spring function in Svelte creates a store whose value is animated, with a motion that simulates the behavior of a spring. This means when the value changes, instead of transitioning at a steady rate, it \"bounces\" like a spring would, depending on the physics parameters provided. This adds a level of realism to the transitions and can enhance the user experience.\n *\n * @deprecated Use [`Spring`](https://svelte.dev/docs/svelte/svelte-motion#Spring) instead\n * @template [T=any]\n * @param {T} [value]\n * @param {SpringOpts} [opts]\n * @returns {SpringStore<T>}\n */\nexport function spring(value, opts = {}) {\n\tconst store = writable(value);\n\tconst { stiffness = 0.15, damping = 0.8, precision = 0.01 } = opts;\n\t/** @type {number} */\n\tlet last_time;\n\t/** @type {Task | null} */\n\tlet task;\n\t/** @type {object} */\n\tlet current_token;\n\n\tlet last_value = /** @type {T} */ (value);\n\tlet target_value = /** @type {T | undefined} */ (value);\n\n\tlet inv_mass = 1;\n\tlet inv_mass_recovery_rate = 0;\n\tlet cancel_task = false;\n\t/**\n\t * @param {T} new_value\n\t * @param {SpringUpdateOpts} opts\n\t * @returns {Promise<void>}\n\t */\n\tfunction set(new_value, opts = {}) {\n\t\ttarget_value = new_value;\n\t\tconst token = (current_token = {});\n\t\tif (value == null || opts.hard || (spring.stiffness >= 1 && spring.damping >= 1)) {\n\t\t\tcancel_task = true; // cancel any running animation\n\t\t\tlast_time = raf.now();\n\t\t\tlast_value = new_value;\n\t\t\tstore.set((value = target_value));\n\t\t\treturn Promise.resolve();\n\t\t} else if (opts.soft) {\n\t\t\tconst rate = opts.soft === true ? 0.5 : +opts.soft;\n\t\t\tinv_mass_recovery_rate = 1 / (rate * 60);\n\t\t\tinv_mass = 0; // infinite mass, unaffected by spring forces\n\t\t}\n\t\tif (!task) {\n\t\t\tlast_time = raf.now();\n\t\t\tcancel_task = false;\n\t\t\ttask = loop((now) => {\n\t\t\t\tif (cancel_task) {\n\t\t\t\t\tcancel_task = false;\n\t\t\t\t\ttask = null;\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tinv_mass = Math.min(inv_mass + inv_mass_recovery_rate, 1);\n\n\t\t\t\t// clamp elapsed time to 1/30th of a second, so that longer pauses\n\t\t\t\t// (blocked thread or inactive tab) don't cause the spring to go haywire\n\t\t\t\tconst elapsed = Math.min(now - last_time, 1000 / 30);\n\n\t\t\t\t/** @type {TickContext} */\n\t\t\t\tconst ctx = {\n\t\t\t\t\tinv_mass,\n\t\t\t\t\topts: spring,\n\t\t\t\t\tsettled: true,\n\t\t\t\t\tdt: (elapsed * 60) / 1000\n\t\t\t\t};\n\t\t\t\t// @ts-ignore\n\t\t\t\tconst next_value = tick_spring(ctx, last_value, value, target_value);\n\t\t\t\tlast_time = now;\n\t\t\t\tlast_value = /** @type {T} */ (value);\n\t\t\t\tstore.set((value = /** @type {T} */ (next_value)));\n\t\t\t\tif (ctx.settled) {\n\t\t\t\t\ttask = null;\n\t\t\t\t}\n\t\t\t\treturn !ctx.settled;\n\t\t\t});\n\t\t}\n\t\treturn new Promise((fulfil) => {\n\t\t\t/** @type {Task} */ (task).promise.then(() => {\n\t\t\t\tif (token === current_token) fulfil();\n\t\t\t});\n\t\t});\n\t}\n\t/** @type {SpringStore<T>} */\n\t// @ts-expect-error - class-only properties are missing\n\tconst spring = {\n\t\tset,\n\t\tupdate: (fn, opts) => set(fn(/** @type {T} */ (target_value), /** @type {T} */ (value)), opts),\n\t\tsubscribe: store.subscribe,\n\t\tstiffness,\n\t\tdamping,\n\t\tprecision\n\t};\n\treturn spring;\n}\n\n/**\n * A wrapper for a value that behaves in a spring-like fashion. Changes to `spring.target` will cause `spring.current` to\n * move towards it over time, taking account of the `spring.stiffness` and `spring.damping` parameters.\n *\n * ```svelte\n * <script>\n * \timport { Spring } from 'svelte/motion';\n *\n * \tconst spring = new Spring(0);\n * </script>\n *\n * <input type=\"range\" bind:value={spring.target} />\n * <input type=\"range\" bind:value={spring.current} disabled />\n * ```\n * @template T\n * @since 5.8.0\n */\nexport class Spring {\n\t#stiffness = state(0.15);\n\t#damping = state(0.8);\n\t#precision = state(0.01);\n\n\t#current;\n\t#target;\n\n\t#last_value = /** @type {T} */ (undefined);\n\t#last_time = 0;\n\n\t#inverse_mass = 1;\n\t#momentum = 0;\n\n\t/** @type {import('../internal/client/types').Task | null} */\n\t#task = null;\n\n\t/** @type {ReturnType<typeof deferred> | null} */\n\t#deferred = null;\n\n\t/**\n\t * @param {T} value\n\t * @param {SpringOpts} [options]\n\t */\n\tconstructor(value, options = {}) {\n\t\tthis.#current = DEV ? tag(state(value), 'Spring.current') : state(value);\n\t\tthis.#target = DEV ? tag(state(value), 'Spring.target') : state(value);\n\n\t\tif (typeof options.stiffness === 'number') this.#stiffness.v = clamp(options.stiffness, 0, 1);\n\t\tif (typeof options.damping === 'number') this.#damping.v = clamp(options.damping, 0, 1);\n\t\tif (typeof options.precision === 'number') this.#precision.v = options.precision;\n\n\t\tif (DEV) {\n\t\t\ttag(this.#stiffness, 'Spring.stiffness');\n\t\t\ttag(this.#damping, 'Spring.damping');\n\t\t\ttag(this.#precision, 'Spring.precision');\n\t\t\ttag(this.#current, 'Spring.current');\n\t\t\ttag(this.#target, 'Spring.target');\n\t\t}\n\t}\n\n\t/**\n\t * Create a spring whose value is bound to the return value of `fn`. This must be called\n\t * inside an effect root (for example, during component initialisation).\n\t *\n\t * ```svelte\n\t * <script>\n\t * \timport { Spring } from 'svelte/motion';\n\t *\n\t * \tlet { number } = $props();\n\t *\n\t * \tconst spring = Spring.of(() => number);\n\t * </script>\n\t * ```\n\t * @template U\n\t * @param {() => U} fn\n\t * @param {SpringOpts} [options]\n\t */\n\tstatic of(fn, options) {\n\t\tconst spring = new Spring(fn(), options);\n\n\t\trender_effect(() => {\n\t\t\tspring.set(fn());\n\t\t});\n\n\t\treturn spring;\n\t}\n\n\t/** @param {T} value */\n\t#update(value) {\n\t\tset(this.#target, value);\n\n\t\tthis.#current.v ??= value;\n\t\tthis.#last_value ??= this.#current.v;\n\n\t\tif (!this.#task) {\n\t\t\tthis.#last_time = raf.now();\n\n\t\t\tvar inv_mass_recovery_rate = 1000 / (this.#momentum * 60);\n\n\t\t\tthis.#task ??= loop((now) => {\n\t\t\t\tthis.#inverse_mass = Math.min(this.#inverse_mass + inv_mass_recovery_rate, 1);\n\n\t\t\t\t// clamp elapsed time to 1/30th of a second, so that longer pauses\n\t\t\t\t// (blocked thread or inactive tab) don't cause the spring to go haywire\n\t\t\t\tconst elapsed = Math.min(now - this.#last_time, 1000 / 30);\n\n\t\t\t\t/** @type {import('./private').TickContext} */\n\t\t\t\tconst ctx = {\n\t\t\t\t\tinv_mass: this.#inverse_mass,\n\t\t\t\t\topts: {\n\t\t\t\t\t\tstiffness: this.#stiffness.v,\n\t\t\t\t\t\tdamping: this.#damping.v,\n\t\t\t\t\t\tprecision: this.#precision.v\n\t\t\t\t\t},\n\t\t\t\t\tsettled: true,\n\t\t\t\t\tdt: (elapsed * 60) / 1000\n\t\t\t\t};\n\n\t\t\t\tvar next = tick_spring(ctx, this.#last_value, this.#current.v, this.#target.v);\n\t\t\t\tthis.#last_value = this.#current.v;\n\t\t\t\tthis.#last_time = now;\n\t\t\t\tset(this.#current, next);\n\n\t\t\t\tif (ctx.settled) {\n\t\t\t\t\tthis.#task = null;\n\t\t\t\t}\n\n\t\t\t\treturn !ctx.settled;\n\t\t\t});\n\t\t}\n\n\t\treturn this.#task.promise;\n\t}\n\n\t/**\n\t * Sets `spring.target` to `value` and returns a `Promise` that resolves if and when `spring.current` catches up to it.\n\t *\n\t * If `options.instant` is `true`, `spring.current` immediately matches `spring.target`.\n\t *\n\t * If `options.preserveMomentum` is provided, the spring will continue on its current trajectory for\n\t * the specified number of milliseconds. This is useful for things like 'fling' gestures.\n\t *\n\t * @param {T} value\n\t * @param {SpringUpdateOpts} [options]\n\t */\n\tset(value, options) {\n\t\tthis.#deferred?.reject(new Error('Aborted'));\n\n\t\tif (options?.instant || this.#current.v === undefined) {\n\t\t\tthis.#task?.abort();\n\t\t\tthis.#task = null;\n\t\t\tset(this.#current, set(this.#target, value));\n\t\t\tthis.#last_value = value;\n\t\t\treturn Promise.resolve();\n\t\t}\n\n\t\tif (options?.preserveMomentum) {\n\t\t\tthis.#inverse_mass = 0;\n\t\t\tthis.#momentum = options.preserveMomentum;\n\t\t}\n\n\t\tvar d = (this.#deferred = deferred());\n\t\td.promise.catch(noop);\n\n\t\tthis.#update(value).then(() => {\n\t\t\tif (d !== this.#deferred) return;\n\t\t\td.resolve(undefined);\n\t\t});\n\n\t\treturn d.promise;\n\t}\n\n\tget current() {\n\t\treturn get(this.#current);\n\t}\n\n\tget damping() {\n\t\treturn get(this.#damping);\n\t}\n\n\tset damping(v) {\n\t\tset(this.#damping, clamp(v, 0, 1));\n\t}\n\n\tget precision() {\n\t\treturn get(this.#precision);\n\t}\n\n\tset precision(v) {\n\t\tset(this.#precision, v);\n\t}\n\n\tget stiffness() {\n\t\treturn get(this.#stiffness);\n\t}\n\n\tset stiffness(v) {\n\t\tset(this.#stiffness, clamp(v, 0, 1));\n\t}\n\n\tget target() {\n\t\treturn get(this.#target);\n\t}\n\n\tset target(v) {\n\t\tthis.set(v);\n\t}\n}\n\n/**\n * @param {number} n\n * @param {number} min\n * @param {number} max\n */\nfunction clamp(n, min, max) {\n\treturn Math.max(min, Math.min(max, n));\n}\n", "/** @import { Task } from '../internal/client/types' */\n/** @import { Tweened } from './public' */\n/** @import { TweenedOptions } from './private' */\nimport { writable } from '../store/shared/index.js';\nimport { raf } from '../internal/client/timing.js';\nimport { loop } from '../internal/client/loop.js';\nimport { linear } from '../easing/index.js';\nimport { is_date } from './utils.js';\nimport { set, state } from '../internal/client/reactivity/sources.js';\nimport { tag } from '../internal/client/dev/tracing.js';\nimport { get, render_effect } from 'svelte/internal/client';\nimport { DEV } from 'esm-env';\n\n/**\n * @template T\n * @param {T} a\n * @param {T} b\n * @returns {(t: number) => T}\n */\nfunction get_interpolator(a, b) {\n\tif (a === b || a !== a) return () => a;\n\n\tconst type = typeof a;\n\tif (type !== typeof b || Array.isArray(a) !== Array.isArray(b)) {\n\t\tthrow new Error('Cannot interpolate values of different type');\n\t}\n\n\tif (Array.isArray(a)) {\n\t\tconst arr = /** @type {Array<any>} */ (b).map((bi, i) => {\n\t\t\treturn get_interpolator(/** @type {Array<any>} */ (a)[i], bi);\n\t\t});\n\n\t\t// @ts-ignore\n\t\treturn (t) => arr.map((fn) => fn(t));\n\t}\n\n\tif (type === 'object') {\n\t\tif (!a || !b) {\n\t\t\tthrow new Error('Object cannot be null');\n\t\t}\n\n\t\tif (is_date(a) && is_date(b)) {\n\t\t\tconst an = a.getTime();\n\t\t\tconst bn = b.getTime();\n\t\t\tconst delta = bn - an;\n\n\t\t\t// @ts-ignore\n\t\t\treturn (t) => new Date(an + t * delta);\n\t\t}\n\n\t\tconst keys = Object.keys(b);\n\n\t\t/** @type {Record<string, (t: number) => T>} */\n\t\tconst interpolators = {};\n\t\tkeys.forEach((key) => {\n\t\t\t// @ts-ignore\n\t\t\tinterpolators[key] = get_interpolator(a[key], b[key]);\n\t\t});\n\n\t\t// @ts-ignore\n\t\treturn (t) => {\n\t\t\t/** @type {Record<string, any>} */\n\t\t\tconst result = {};\n\t\t\tkeys.forEach((key) => {\n\t\t\t\tresult[key] = interpolators[key](t);\n\t\t\t});\n\t\t\treturn result;\n\t\t};\n\t}\n\n\tif (type === 'number') {\n\t\tconst delta = /** @type {number} */ (b) - /** @type {number} */ (a);\n\t\t// @ts-ignore\n\t\treturn (t) => a + t * delta;\n\t}\n\n\t// for non-numeric values, snap to the final value immediately\n\treturn () => b;\n}\n\n/**\n * A tweened store in Svelte is a special type of store that provides smooth transitions between state values over time.\n *\n * @deprecated Use [`Tween`](https://svelte.dev/docs/svelte/svelte-motion#Tween) instead\n * @template T\n * @param {T} [value]\n * @param {TweenedOptions<T>} [defaults]\n * @returns {Tweened<T>}\n */\nexport function tweened(value, defaults = {}) {\n\tconst store = writable(value);\n\t/** @type {Task} */\n\tlet task;\n\tlet target_value = value;\n\t/**\n\t * @param {T} new_value\n\t * @param {TweenedOptions<T>} [opts]\n\t */\n\tfunction set(new_value, opts) {\n\t\ttarget_value = new_value;\n\n\t\tif (value == null) {\n\t\t\tstore.set((value = new_value));\n\t\t\treturn Promise.resolve();\n\t\t}\n\n\t\t/** @type {Task | null} */\n\t\tlet previous_task = task;\n\n\t\tlet started = false;\n\t\tlet {\n\t\t\tdelay = 0,\n\t\t\tduration = 400,\n\t\t\teasing = linear,\n\t\t\tinterpolate = get_interpolator\n\t\t} = { ...defaults, ...opts };\n\n\t\tif (duration === 0) {\n\t\t\tif (previous_task) {\n\t\t\t\tprevious_task.abort();\n\t\t\t\tprevious_task = null;\n\t\t\t}\n\t\t\tstore.set((value = target_value));\n\t\t\treturn Promise.resolve();\n\t\t}\n\n\t\tconst start = raf.now() + delay;\n\n\t\t/** @type {(t: number) => T} */\n\t\tlet fn;\n\t\ttask = loop((now) => {\n\t\t\tif (now < start) return true;\n\t\t\tif (!started) {\n\t\t\t\tfn = interpolate(/** @type {any} */ (value), new_value);\n\t\t\t\tif (typeof duration === 'function')\n\t\t\t\t\tduration = duration(/** @type {any} */ (value), new_value);\n\t\t\t\tstarted = true;\n\t\t\t}\n\t\t\tif (previous_task) {\n\t\t\t\tprevious_task.abort();\n\t\t\t\tprevious_task = null;\n\t\t\t}\n\t\t\tconst elapsed = now - start;\n\t\t\tif (elapsed > /** @type {number} */ (duration)) {\n\t\t\t\tstore.set((value = new_value));\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t// @ts-ignore\n\t\t\tstore.set((value = fn(easing(elapsed / duration))));\n\t\t\treturn true;\n\t\t});\n\t\treturn task.promise;\n\t}\n\treturn {\n\t\tset,\n\t\tupdate: (fn, opts) =>\n\t\t\tset(fn(/** @type {any} */ (target_value), /** @type {any} */ (value)), opts),\n\t\tsubscribe: store.subscribe\n\t};\n}\n\n/**\n * A wrapper for a value that tweens smoothly to its target value. Changes to `tween.target` will cause `tween.current` to\n * move towards it over time, taking account of the `delay`, `duration` and `easing` options.\n *\n * ```svelte\n * <script>\n * \timport { Tween } from 'svelte/motion';\n *\n * \tconst tween = new Tween(0);\n * </script>\n *\n * <input type=\"range\" bind:value={tween.target} />\n * <input type=\"range\" bind:value={tween.current} disabled />\n * ```\n * @template T\n * @since 5.8.0\n */\nexport class Tween {\n\t#current;\n\t#target;\n\n\t/** @type {TweenedOptions<T>} */\n\t#defaults;\n\n\t/** @type {import('../internal/client/types').Task | null} */\n\t#task = null;\n\n\t/**\n\t * @param {T} value\n\t * @param {TweenedOptions<T>} options\n\t */\n\tconstructor(value, options = {}) {\n\t\tthis.#current = state(value);\n\t\tthis.#target = state(value);\n\t\tthis.#defaults = options;\n\n\t\tif (DEV) {\n\t\t\ttag(this.#current, 'Tween.current');\n\t\t\ttag(this.#target, 'Tween.target');\n\t\t}\n\t}\n\n\t/**\n\t * Create a tween whose value is bound to the return value of `fn`. This must be called\n\t * inside an effect root (for example, during component initialisation).\n\t *\n\t * ```svelte\n\t * <script>\n\t * \timport { Tween } from 'svelte/motion';\n\t *\n\t * \tlet { number } = $props();\n\t *\n\t * \tconst tween = Tween.of(() => number);\n\t * </script>\n\t * ```\n\t * @template U\n\t * @param {() => U} fn\n\t * @param {TweenedOptions<U>} [options]\n\t */\n\tstatic of(fn, options) {\n\t\tconst tween = new Tween(fn(), options);\n\n\t\trender_effect(() => {\n\t\t\ttween.set(fn());\n\t\t});\n\n\t\treturn tween;\n\t}\n\n\t/**\n\t * Sets `tween.target` to `value` and returns a `Promise` that resolves if and when `tween.current` catches up to it.\n\t *\n\t * If `options` are provided, they will override the tween's defaults.\n\t * @param {T} value\n\t * @param {TweenedOptions<T>} [options]\n\t * @returns\n\t */\n\tset(value, options) {\n\t\tset(this.#target, value);\n\n\t\tlet {\n\t\t\tdelay = 0,\n\t\t\tduration = 400,\n\t\t\teasing = linear,\n\t\t\tinterpolate = get_interpolator\n\t\t} = { ...this.#defaults, ...options };\n\n\t\tif (duration === 0) {\n\t\t\tthis.#task?.abort();\n\t\t\tset(this.#current, value);\n\t\t\treturn Promise.resolve();\n\t\t}\n\n\t\tconst start = raf.now() + delay;\n\n\t\t/** @type {(t: number) => T} */\n\t\tlet fn;\n\t\tlet started = false;\n\t\tlet previous_task = this.#task;\n\n\t\tthis.#task = loop((now) => {\n\t\t\tif (now < start) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tif (!started) {\n\t\t\t\tstarted = true;\n\n\t\t\t\tconst prev = this.#current.v;\n\n\t\t\t\tfn = interpolate(prev, value);\n\n\t\t\t\tif (typeof duration === 'function') {\n\t\t\t\t\tduration = duration(prev, value);\n\t\t\t\t}\n\n\t\t\t\tprevious_task?.abort();\n\t\t\t}\n\n\t\t\tconst elapsed = now - start;\n\n\t\t\tif (elapsed > /** @type {number} */ (duration)) {\n\t\t\t\tset(this.#current, value);\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\tset(this.#current, fn(easing(elapsed / /** @type {number} */ (duration))));\n\t\t\treturn true;\n\t\t});\n\n\t\treturn this.#task.promise;\n\t}\n\n\tget current() {\n\t\treturn get(this.#current);\n\t}\n\n\tget target() {\n\t\treturn get(this.#target);\n\t}\n\n\tset target(v) {\n\t\tthis.set(v);\n\t}\n}\n", "import { MediaQuery } from 'svelte/reactivity';\n\nexport * from './spring.js';\nexport * from './tweened.js';\n\n/**\n * A [media query](https://svelte.dev/docs/svelte/svelte-reactivity#MediaQuery) that matches if the user [prefers reduced motion](https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-reduced-motion).\n *\n * ```svelte\n * <script>\n * \timport { prefersReducedMotion } from 'svelte/motion';\n * \timport { fly } from 'svelte/transition';\n *\n * \tlet visible = $state(false);\n * </script>\n *\n * <button onclick={() => visible = !visible}>\n * \ttoggle\n * </button>\n *\n * {#if visible}\n * \t<p transition:fly={{ y: prefersReducedMotion.current ? 0 : 200 }}>\n * \t\tflies in, unless the user prefers reduced motion\n * \t</p>\n * {/if}\n * ```\n * @type {MediaQuery}\n * @since 5.7.0\n */\nexport const prefersReducedMotion = /*@__PURE__*/ new MediaQuery(\n\t'(prefers-reduced-motion: reduce)'\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIO,SAAS,QAAQ,KAAK;AAC5B,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AAChD;;;ACgBA,SAAS,YAAY,KAAK,YAAY,eAAe,cAAc;AAClE,MAAI,OAAO,kBAAkB,YAAY,QAAQ,aAAa,GAAG;AAEhE,UAAM,QAAQ,eAAe;AAE7B,UAAM,YAAY,gBAAgB,eAAe,IAAI,MAAM,IAAI;AAC/D,UAAMA,UAAS,IAAI,KAAK,YAAY;AACpC,UAAM,SAAS,IAAI,KAAK,UAAU;AAClC,UAAM,gBAAgBA,UAAS,UAAU,IAAI;AAC7C,UAAM,KAAK,WAAW,gBAAgB,IAAI;AAC1C,QAAI,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,aAAa,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,WAAW;AAC7E,aAAO;AAAA,IACR,OAAO;AACN,UAAI,UAAU;AAEd,aAAO,QAAQ,aAAa,IAAI,IAAI,KAAK,cAAc,QAAQ,IAAI,CAAC,IAAI,gBAAgB;AAAA,IACzF;AAAA,EACD,WAAW,MAAM,QAAQ,aAAa,GAAG;AAExC,WAAO,cAAc;AAAA,MAAI,CAAC,GAAG;AAAA;AAAA,QAE5B,YAAY,KAAK,WAAW,CAAC,GAAG,cAAc,CAAC,GAAG,aAAa,CAAC,CAAC;AAAA;AAAA,IAClE;AAAA,EACD,WAAW,OAAO,kBAAkB,UAAU;AAC7C,UAAM,aAAa,CAAC;AACpB,eAAW,KAAK,eAAe;AAE9B,iBAAW,CAAC,IAAI,YAAY,KAAK,WAAW,CAAC,GAAG,cAAc,CAAC,GAAG,aAAa,CAAC,CAAC;AAAA,IAClF;AAEA,WAAO;AAAA,EACR,OAAO;AACN,UAAM,IAAI,MAAM,iBAAiB,OAAO,aAAa,SAAS;AAAA,EAC/D;AACD;AAWO,SAAS,OAAO,OAAO,OAAO,CAAC,GAAG;AACxC,QAAM,QAAQ,SAAS,KAAK;AAC5B,QAAM,EAAE,YAAY,MAAM,UAAU,KAAK,YAAY,KAAK,IAAI;AAE9D,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAAA;AAAA,IAA+B;AAAA;AACnC,MAAI;AAAA;AAAA,IAA6C;AAAA;AAEjD,MAAI,WAAW;AACf,MAAI,yBAAyB;AAC7B,MAAI,cAAc;AAMlB,WAASC,KAAI,WAAWC,QAAO,CAAC,GAAG;AAClC,mBAAe;AACf,UAAM,QAAS,gBAAgB,CAAC;AAChC,QAAI,SAAS,QAAQA,MAAK,QAASF,QAAO,aAAa,KAAKA,QAAO,WAAW,GAAI;AACjF,oBAAc;AACd,kBAAY,IAAI,IAAI;AACpB,mBAAa;AACb,YAAM,IAAK,QAAQ,YAAa;AAChC,aAAO,QAAQ,QAAQ;AAAA,IACxB,WAAWE,MAAK,MAAM;AACrB,YAAM,OAAOA,MAAK,SAAS,OAAO,MAAM,CAACA,MAAK;AAC9C,+BAAyB,KAAK,OAAO;AACrC,iBAAW;AAAA,IACZ;AACA,QAAI,CAAC,MAAM;AACV,kBAAY,IAAI,IAAI;AACpB,oBAAc;AACd,aAAO,KAAK,CAAC,QAAQ;AACpB,YAAI,aAAa;AAChB,wBAAc;AACd,iBAAO;AACP,iBAAO;AAAA,QACR;AACA,mBAAW,KAAK,IAAI,WAAW,wBAAwB,CAAC;AAIxD,cAAM,UAAU,KAAK,IAAI,MAAM,WAAW,MAAO,EAAE;AAGnD,cAAM,MAAM;AAAA,UACX;AAAA,UACA,MAAMF;AAAA,UACN,SAAS;AAAA,UACT,IAAK,UAAU,KAAM;AAAA,QACtB;AAEA,cAAM,aAAa,YAAY,KAAK,YAAY,OAAO,YAAY;AACnE,oBAAY;AACZ;AAAA,QAA+B;AAC/B,cAAM,IAAK;AAAA,QAA0B,UAAY;AACjD,YAAI,IAAI,SAAS;AAChB,iBAAO;AAAA,QACR;AACA,eAAO,CAAC,IAAI;AAAA,MACb,CAAC;AAAA,IACF;AACA,WAAO,IAAI,QAAQ,CAAC,WAAW;AACV,MAAC,KAAM,QAAQ,KAAK,MAAM;AAC7C,YAAI,UAAU,cAAe,QAAO;AAAA,MACrC,CAAC;AAAA,IACF,CAAC;AAAA,EACF;AAGA,QAAMA,UAAS;AAAA,IACd,KAAAC;AAAA,IACA,QAAQ,CAAC,IAAIC,UAASD,KAAI;AAAA;AAAA,MAAqB;AAAA;AAAA,MAAiC;AAAA,IAAM,GAAGC,KAAI;AAAA,IAC7F,WAAW,MAAM;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACA,SAAOF;AACR;AAmBO,IAAM,SAAN,MAAM,QAAO;AAAA,EACnB,aAAa,MAAM,IAAI;AAAA,EACvB,WAAW,MAAM,GAAG;AAAA,EACpB,aAAa,MAAM,IAAI;AAAA,EAEvB;AAAA,EACA;AAAA,EAEA;AAAA;AAAA,IAAgC;AAAA;AAAA,EAChC,aAAa;AAAA,EAEb,gBAAgB;AAAA,EAChB,YAAY;AAAA;AAAA,EAGZ,QAAQ;AAAA;AAAA,EAGR,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,YAAY,OAAO,UAAU,CAAC,GAAG;AAChC,SAAK,WAAW,eAAM,IAAI,MAAM,KAAK,GAAG,gBAAgB,IAAI,MAAM,KAAK;AACvE,SAAK,UAAU,eAAM,IAAI,MAAM,KAAK,GAAG,eAAe,IAAI,MAAM,KAAK;AAErE,QAAI,OAAO,QAAQ,cAAc,SAAU,MAAK,WAAW,IAAI,MAAM,QAAQ,WAAW,GAAG,CAAC;AAC5F,QAAI,OAAO,QAAQ,YAAY,SAAU,MAAK,SAAS,IAAI,MAAM,QAAQ,SAAS,GAAG,CAAC;AACtF,QAAI,OAAO,QAAQ,cAAc,SAAU,MAAK,WAAW,IAAI,QAAQ;AAEvE,QAAI,cAAK;AACR,UAAI,KAAK,YAAY,kBAAkB;AACvC,UAAI,KAAK,UAAU,gBAAgB;AACnC,UAAI,KAAK,YAAY,kBAAkB;AACvC,UAAI,KAAK,UAAU,gBAAgB;AACnC,UAAI,KAAK,SAAS,eAAe;AAAA,IAClC;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,OAAO,GAAG,IAAI,SAAS;AACtB,UAAMA,UAAS,IAAI,QAAO,GAAG,GAAG,OAAO;AAEvC,kBAAc,MAAM;AACnB,MAAAA,QAAO,IAAI,GAAG,CAAC;AAAA,IAChB,CAAC;AAED,WAAOA;AAAA,EACR;AAAA;AAAA,EAGA,QAAQ,OAAO;AACd,QAAI,KAAK,SAAS,KAAK;AAEvB,SAAK,SAAS,MAAM;AACpB,SAAK,gBAAgB,KAAK,SAAS;AAEnC,QAAI,CAAC,KAAK,OAAO;AAChB,WAAK,aAAa,IAAI,IAAI;AAE1B,UAAI,yBAAyB,OAAQ,KAAK,YAAY;AAEtD,WAAK,UAAU,KAAK,CAAC,QAAQ;AAC5B,aAAK,gBAAgB,KAAK,IAAI,KAAK,gBAAgB,wBAAwB,CAAC;AAI5E,cAAM,UAAU,KAAK,IAAI,MAAM,KAAK,YAAY,MAAO,EAAE;AAGzD,cAAM,MAAM;AAAA,UACX,UAAU,KAAK;AAAA,UACf,MAAM;AAAA,YACL,WAAW,KAAK,WAAW;AAAA,YAC3B,SAAS,KAAK,SAAS;AAAA,YACvB,WAAW,KAAK,WAAW;AAAA,UAC5B;AAAA,UACA,SAAS;AAAA,UACT,IAAK,UAAU,KAAM;AAAA,QACtB;AAEA,YAAI,OAAO,YAAY,KAAK,KAAK,aAAa,KAAK,SAAS,GAAG,KAAK,QAAQ,CAAC;AAC7E,aAAK,cAAc,KAAK,SAAS;AACjC,aAAK,aAAa;AAClB,YAAI,KAAK,UAAU,IAAI;AAEvB,YAAI,IAAI,SAAS;AAChB,eAAK,QAAQ;AAAA,QACd;AAEA,eAAO,CAAC,IAAI;AAAA,MACb,CAAC;AAAA,IACF;AAEA,WAAO,KAAK,MAAM;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,IAAI,OAAO,SAAS;AACnB,SAAK,WAAW,OAAO,IAAI,MAAM,SAAS,CAAC;AAE3C,QAAI,SAAS,WAAW,KAAK,SAAS,MAAM,QAAW;AACtD,WAAK,OAAO,MAAM;AAClB,WAAK,QAAQ;AACb,UAAI,KAAK,UAAU,IAAI,KAAK,SAAS,KAAK,CAAC;AAC3C,WAAK,cAAc;AACnB,aAAO,QAAQ,QAAQ;AAAA,IACxB;AAEA,QAAI,SAAS,kBAAkB;AAC9B,WAAK,gBAAgB;AACrB,WAAK,YAAY,QAAQ;AAAA,IAC1B;AAEA,QAAI,IAAK,KAAK,YAAY,SAAS;AACnC,MAAE,QAAQ,MAAM,IAAI;AAEpB,SAAK,QAAQ,KAAK,EAAE,KAAK,MAAM;AAC9B,UAAI,MAAM,KAAK,UAAW;AAC1B,QAAE,QAAQ,MAAS;AAAA,IACpB,CAAC;AAED,WAAO,EAAE;AAAA,EACV;AAAA,EAEA,IAAI,UAAU;AACb,WAAO,IAAI,KAAK,QAAQ;AAAA,EACzB;AAAA,EAEA,IAAI,UAAU;AACb,WAAO,IAAI,KAAK,QAAQ;AAAA,EACzB;AAAA,EAEA,IAAI,QAAQ,GAAG;AACd,QAAI,KAAK,UAAU,MAAM,GAAG,GAAG,CAAC,CAAC;AAAA,EAClC;AAAA,EAEA,IAAI,YAAY;AACf,WAAO,IAAI,KAAK,UAAU;AAAA,EAC3B;AAAA,EAEA,IAAI,UAAU,GAAG;AAChB,QAAI,KAAK,YAAY,CAAC;AAAA,EACvB;AAAA,EAEA,IAAI,YAAY;AACf,WAAO,IAAI,KAAK,UAAU;AAAA,EAC3B;AAAA,EAEA,IAAI,UAAU,GAAG;AAChB,QAAI,KAAK,YAAY,MAAM,GAAG,GAAG,CAAC,CAAC;AAAA,EACpC;AAAA,EAEA,IAAI,SAAS;AACZ,WAAO,IAAI,KAAK,OAAO;AAAA,EACxB;AAAA,EAEA,IAAI,OAAO,GAAG;AACb,SAAK,IAAI,CAAC;AAAA,EACX;AACD;AAOA,SAAS,MAAM,GAAG,KAAK,KAAK;AAC3B,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,CAAC;AACtC;;;AC7VA,SAAS,iBAAiB,GAAG,GAAG;AAC/B,MAAI,MAAM,KAAK,MAAM,EAAG,QAAO,MAAM;AAErC,QAAM,OAAO,OAAO;AACpB,MAAI,SAAS,OAAO,KAAK,MAAM,QAAQ,CAAC,MAAM,MAAM,QAAQ,CAAC,GAAG;AAC/D,UAAM,IAAI,MAAM,6CAA6C;AAAA,EAC9D;AAEA,MAAI,MAAM,QAAQ,CAAC,GAAG;AACrB,UAAM;AAAA;AAAA,MAAiC,EAAG,IAAI,CAAC,IAAI,MAAM;AACxD,eAAO;AAAA;AAAA,UAA4C,EAAG,CAAC;AAAA,UAAG;AAAA,QAAE;AAAA,MAC7D,CAAC;AAAA;AAGD,WAAO,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AAAA,EACpC;AAEA,MAAI,SAAS,UAAU;AACtB,QAAI,CAAC,KAAK,CAAC,GAAG;AACb,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACxC;AAEA,QAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC,GAAG;AAC7B,YAAM,KAAK,EAAE,QAAQ;AACrB,YAAM,KAAK,EAAE,QAAQ;AACrB,YAAM,QAAQ,KAAK;AAGnB,aAAO,CAAC,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,IACtC;AAEA,UAAM,OAAO,OAAO,KAAK,CAAC;AAG1B,UAAM,gBAAgB,CAAC;AACvB,SAAK,QAAQ,CAAC,QAAQ;AAErB,oBAAc,GAAG,IAAI,iBAAiB,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAAA,IACrD,CAAC;AAGD,WAAO,CAAC,MAAM;AAEb,YAAM,SAAS,CAAC;AAChB,WAAK,QAAQ,CAAC,QAAQ;AACrB,eAAO,GAAG,IAAI,cAAc,GAAG,EAAE,CAAC;AAAA,MACnC,CAAC;AACD,aAAO;AAAA,IACR;AAAA,EACD;AAEA,MAAI,SAAS,UAAU;AACtB,UAAM;AAAA;AAAA,MAA+B;AAAA,MAA4B;AAAA;AAEjE,WAAO,CAAC,MAAM,IAAI,IAAI;AAAA,EACvB;AAGA,SAAO,MAAM;AACd;AAWO,SAAS,QAAQ,OAAO,WAAW,CAAC,GAAG;AAC7C,QAAM,QAAQ,SAAS,KAAK;AAE5B,MAAI;AACJ,MAAI,eAAe;AAKnB,WAASG,KAAI,WAAW,MAAM;AAC7B,mBAAe;AAEf,QAAI,SAAS,MAAM;AAClB,YAAM,IAAK,QAAQ,SAAU;AAC7B,aAAO,QAAQ,QAAQ;AAAA,IACxB;AAGA,QAAI,gBAAgB;AAEpB,QAAI,UAAU;AACd,QAAI;AAAA,MACH,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,SAAS;AAAA,MACT,cAAc;AAAA,IACf,IAAI,EAAE,GAAG,UAAU,GAAG,KAAK;AAE3B,QAAI,aAAa,GAAG;AACnB,UAAI,eAAe;AAClB,sBAAc,MAAM;AACpB,wBAAgB;AAAA,MACjB;AACA,YAAM,IAAK,QAAQ,YAAa;AAChC,aAAO,QAAQ,QAAQ;AAAA,IACxB;AAEA,UAAM,QAAQ,IAAI,IAAI,IAAI;AAG1B,QAAI;AACJ,WAAO,KAAK,CAAC,QAAQ;AACpB,UAAI,MAAM,MAAO,QAAO;AACxB,UAAI,CAAC,SAAS;AACb,aAAK;AAAA;AAAA,UAAgC;AAAA,UAAQ;AAAA,QAAS;AACtD,YAAI,OAAO,aAAa;AACvB,qBAAW;AAAA;AAAA,YAA6B;AAAA,YAAQ;AAAA,UAAS;AAC1D,kBAAU;AAAA,MACX;AACA,UAAI,eAAe;AAClB,sBAAc,MAAM;AACpB,wBAAgB;AAAA,MACjB;AACA,YAAM,UAAU,MAAM;AACtB,UAAI;AAAA,MAAiC,UAAW;AAC/C,cAAM,IAAK,QAAQ,SAAU;AAC7B,eAAO;AAAA,MACR;AAEA,YAAM,IAAK,QAAQ,GAAG,OAAO,UAAU,QAAQ,CAAC,CAAE;AAClD,aAAO;AAAA,IACR,CAAC;AACD,WAAO,KAAK;AAAA,EACb;AACA,SAAO;AAAA,IACN,KAAAA;AAAA,IACA,QAAQ,CAAC,IAAI,SACZA,KAAI;AAAA;AAAA,MAAuB;AAAA;AAAA,MAAmC;AAAA,IAAM,GAAG,IAAI;AAAA,IAC5E,WAAW,MAAM;AAAA,EAClB;AACD;AAmBO,IAAM,QAAN,MAAM,OAAM;AAAA,EAClB;AAAA,EACA;AAAA;AAAA,EAGA;AAAA;AAAA,EAGA,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAMR,YAAY,OAAO,UAAU,CAAC,GAAG;AAChC,SAAK,WAAW,MAAM,KAAK;AAC3B,SAAK,UAAU,MAAM,KAAK;AAC1B,SAAK,YAAY;AAEjB,QAAI,cAAK;AACR,UAAI,KAAK,UAAU,eAAe;AAClC,UAAI,KAAK,SAAS,cAAc;AAAA,IACjC;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,OAAO,GAAG,IAAI,SAAS;AACtB,UAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,OAAO;AAErC,kBAAc,MAAM;AACnB,YAAM,IAAI,GAAG,CAAC;AAAA,IACf,CAAC;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,IAAI,OAAO,SAAS;AACnB,QAAI,KAAK,SAAS,KAAK;AAEvB,QAAI;AAAA,MACH,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,SAAS;AAAA,MACT,cAAc;AAAA,IACf,IAAI,EAAE,GAAG,KAAK,WAAW,GAAG,QAAQ;AAEpC,QAAI,aAAa,GAAG;AACnB,WAAK,OAAO,MAAM;AAClB,UAAI,KAAK,UAAU,KAAK;AACxB,aAAO,QAAQ,QAAQ;AAAA,IACxB;AAEA,UAAM,QAAQ,IAAI,IAAI,IAAI;AAG1B,QAAI;AACJ,QAAI,UAAU;AACd,QAAI,gBAAgB,KAAK;AAEzB,SAAK,QAAQ,KAAK,CAAC,QAAQ;AAC1B,UAAI,MAAM,OAAO;AAChB,eAAO;AAAA,MACR;AAEA,UAAI,CAAC,SAAS;AACb,kBAAU;AAEV,cAAM,OAAO,KAAK,SAAS;AAE3B,aAAK,YAAY,MAAM,KAAK;AAE5B,YAAI,OAAO,aAAa,YAAY;AACnC,qBAAW,SAAS,MAAM,KAAK;AAAA,QAChC;AAEA,uBAAe,MAAM;AAAA,MACtB;AAEA,YAAM,UAAU,MAAM;AAEtB,UAAI;AAAA,MAAiC,UAAW;AAC/C,YAAI,KAAK,UAAU,KAAK;AACxB,eAAO;AAAA,MACR;AAEA,UAAI,KAAK,UAAU,GAAG,OAAO;AAAA,MAAiC,QAAS,CAAC,CAAC;AACzE,aAAO;AAAA,IACR,CAAC;AAED,WAAO,KAAK,MAAM;AAAA,EACnB;AAAA,EAEA,IAAI,UAAU;AACb,WAAO,IAAI,KAAK,QAAQ;AAAA,EACzB;AAAA,EAEA,IAAI,SAAS;AACZ,WAAO,IAAI,KAAK,OAAO;AAAA,EACxB;AAAA,EAEA,IAAI,OAAO,GAAG;AACb,SAAK,IAAI,CAAC;AAAA,EACX;AACD;;;ACpRO,IAAM,uBAAqC,IAAI;AAAA,EACrD;AACD;", "names": ["spring", "set", "opts", "set"]}