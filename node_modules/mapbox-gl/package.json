{"name": "mapbox-gl", "description": "A WebGL interactive maps library", "version": "3.13.0", "main": "dist/mapbox-gl.js", "style": "dist/mapbox-gl.css", "types": "dist/mapbox-gl.d.ts", "license": "SEE LICENSE IN LICENSE.txt", "type": "module", "repository": {"type": "git", "url": "git://github.com/mapbox/mapbox-gl-js.git"}, "workspaces": ["src/style-spec", "test/build/typings"], "dependencies": {"@mapbox/jsonlint-lines-primitives": "^2.0.2", "@mapbox/mapbox-gl-supported": "^3.0.0", "@mapbox/point-geometry": "^0.1.0", "@mapbox/tiny-sdf": "^2.0.6", "@mapbox/unitbezier": "^0.0.1", "@mapbox/vector-tile": "^1.3.1", "@mapbox/whoots-js": "^3.1.0", "@types/geojson": "^7946.0.16", "@types/geojson-vt": "^3.2.5", "@types/mapbox__point-geometry": "^0.1.4", "@types/mapbox__vector-tile": "^1.3.4", "@types/pbf": "^3.0.5", "@types/supercluster": "^7.1.3", "cheap-ruler": "^4.0.0", "csscolorparser": "~1.0.3", "earcut": "^3.0.1", "geojson-vt": "^4.0.2", "gl-matrix": "^3.4.3", "grid-index": "^1.1.0", "kdbush": "^4.0.2", "martinez-polygon-clipping": "^0.7.4", "murmurhash-js": "^1.0.0", "pbf": "^3.2.1", "potpack": "^2.0.0", "quickselect": "^3.0.0", "serialize-to-js": "^3.1.2", "supercluster": "^8.0.1", "tinyqueue": "^3.0.0", "vt-pbf": "^3.1.3"}, "devDependencies": {"@eslint/compat": "^1.2.9", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.28.0", "@mapbox/mvt-fixtures": "^3.10.0", "@octokit/rest": "^22.0.0", "@rollup/plugin-commonjs": "^28.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-virtual": "^3.0.2", "@size-limit/file": "^11.2.0", "@tweakpane/core": "^2.0.5", "@types/murmurhash-js": "^1.0.6", "@types/node": "^24.0.3", "@types/offscreencanvas": "^2019.7.3", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "@vitest/browser": "^3.2.3", "@vitest/ui": "^3.2.2", "address": "^2.0.3", "browserify": "^17.0.1", "browserslist-to-esbuild": "^2.1.1", "chalk": "^5.4.1", "chokidar": "^4.0.3", "cssnano": "^7.0.7", "d3-queue": "^3.0.7", "diff": "^8.0.2", "dts-bundle-generator": "^9.5.1", "ejs": "^3.1.10", "envify": "^4.1.0", "esbuild": "^0.25.5", "eslint": "^9.28.0", "eslint-config-mourner": "^4.0.0", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-html": "^8.1.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsdoc": "^51.0.1", "express": "^5.1.0", "glob": "^11.0.3", "is-builtin-module": "^5.0.0", "json-stringify-pretty-compact": "^4.0.0", "lodash": "^4.17.21", "mapbox-gl-styles": "^2.0.2", "minimist": "^1.2.6", "mock-geolocation": "^1.0.11", "node-notifier": "^10.0.1", "npm-font-open-sans": "^1.1.0", "npm-run-all": "^4.1.5", "pixelmatch": "^6.0.0", "playwright": "^1.53.0", "postcss": "^8.5.6", "postcss-cli": "^11.0.1", "postcss-inline-svg": "^6.0.0", "qrcode-terminal": "^0.12.0", "rollup": "^4.41.0", "rollup-plugin-esbuild": "^6.2.1", "rollup-plugin-unassert": "^0.6.0", "serve-static": "^2.2.0", "shuffle-seed": "^1.1.6", "size-limit": "^11.2.0", "st": "^3.0.2", "stylelint": "^16.20.0", "stylelint-config-standard": "^38.0.0", "tsx": "^4.20.3", "tweakpane": "^4.0.5", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1", "utility-types": "^3.11.0", "vite-plugin-arraybuffer": "^0.1.0", "vitest": "^3.2.2"}, "scripts": {"build-dev": "rollup -c --environment BUILD:dev", "watch-dev": "rollup -c --environment BUILD:dev --watch", "build-bench": "rollup -c --environment BUILD:bench,MINIFY:true", "build-prod": "rollup -c --environment BUILD:production", "build-prod-min": "rollup -c --environment BUILD:production,MINIFY:true", "build-csp": "rollup -c rollup.config.csp.js", "build-css": "postcss -o dist/mapbox-gl.css src/css/mapbox-gl.css", "build-style-spec": "npm run build --workspace src/style-spec && mkdir -p dist/style-spec && cp src/style-spec/dist/* dist/style-spec", "build-dts": "dts-bundle-generator --no-banner --export-referenced-types=false --umd-module-name=mapboxgl -o ./dist/mapbox-gl.d.ts ./src/index.ts", "watch-css": "postcss --watch -o dist/mapbox-gl.css src/css/mapbox-gl.css", "build-token": "node build/generate-access-token-script.js", "start-server": "st --no-cache -H 0.0.0.0 --port 9966 --index index.html .", "start-range-server": "node build/range-request-server.js", "start": "run-p build-token watch-css watch-dev start-server", "start-debug": "run-p build-token watch-css watch-dev start-server", "prepare-release-pages": "cp -r dist test/release && while read l; do cp debug/$l test/release/$l; done < test/release/local_release_page_list.txt", "start-release": "run-s build-token build-prod-min build-css print-release-url prepare-release-pages start-server", "lint": "eslint --cache src 3d-style", "lint-css": "stylelint 'src/css/mapbox-gl.css'", "test": "run-s lint lint-css test-typings test-unit", "test-suite": "run-s test-render test-query test-expressions", "test-suite-clean": "find test/integration/{render,query, expressions}-tests -mindepth 2 -type d -exec test -e \"{}/actual.png\" \\; -not \\( -exec test -e \"{}/style.json\" \\; \\) -print | xargs -t rm -r", "watch-unit": "vitest --config vitest.config.unit.ts", "test-unit": "vitest --config vitest.config.unit.ts --run", "test-usvg": "vitest --config vitest.config.usvg.ts --run", "start-usvg": "esbuild src/data/usvg/usvg_pb_renderer.ts src/data/usvg/usvg_pb_decoder.ts --outdir=src/data/usvg/ --bundle --format=esm --watch --serve=9966 --servedir=.", "test-build": "node --test --test-timeout 10000 test/build/*.test.js", "test-csp": "npm run build-token && vitest --config vitest.config.csp.ts --run", "watch-render": "npx vitest watch --config vitest.config.render.chromium.ts", "pretest-render": "npm run build-dev", "test-render": "npx vitest run --config vitest.config.render.chromium.ts", "test-render-chromium": "npx vitest run --config vitest.config.render.chromium.ts", "test-render-firefox": "npx vitest run --config vitest.config.render.firefox.ts", "test-render-safari": "npx vitest run --config vitest.config.render.safari.ts", "pretest-render-prod": "npm run build-prod-min", "test-render-prod": "npx vitest run --config vitest.config.render.chromium.prod.ts", "test-render-chromium-prod": "npx vitest run --config vitest.config.render.chromium.prod.ts", "pretest-render-csp": "npm run build-csp", "test-render-csp": "npx vitest run --config vitest.config.render.chromium.csp.ts", "test-render-chromium-csp": "npx vitest run --config vitest.config.render.chromium.csp.ts", "watch-query": "npx vitest watch --config vitest.config.query.ts", "test-query": "npx vitest run --config vitest.config.query.ts", "test-expressions": "tsx ./test/expression.test.ts", "test-typings": "run-s build-typed-style-spec tsc", "test-style-spec": "npm test --workspace src/style-spec", "prepublishOnly": "run-s build-dev build-prod-min build-prod build-csp build-css build-style-spec build-dts", "print-release-url": "node build/print-release-url.js", "size": "size-limit", "check-ts-suppressions": "node build/check-ts-suppressions.js", "codegen": "tsx ./build/generate-style-code.ts && tsx ./build/generate-struct-arrays.ts", "build-typed-style-spec": "tsx ./build/generate-typed-style-spec.ts", "tsc": "tsc --project tsconfig.json"}, "files": ["dist/mapbox-gl*", "dist/style-spec/", "dist/package.json", "LICENSE.txt"], "size-limit": [{"limit": "450 kb", "gzip": true, "path": "dist/mapbox-gl.js"}, {"limit": "5 kb", "gzip": true, "path": "dist/mapbox-gl.css"}]}