import { unionWith } from "eslint-visitor-keys";
const svelteKeys = {
    Program: ["body"],
    SvelteScriptElement: ["name", "startTag", "body", "endTag"],
    SvelteStyleElement: ["name", "startTag", "children", "endTag"],
    SvelteElement: ["name", "startTag", "children", "endTag"],
    SvelteStartTag: ["attributes"],
    SvelteEndTag: [],
    SvelteName: [],
    SvelteMemberExpressionName: ["object", "property"],
    SvelteLiteral: [],
    SvelteMustacheTag: ["expression"],
    SvelteDebugTag: ["identifiers"],
    SvelteConstTag: ["declarations"],
    SvelteRenderTag: ["expression"],
    SvelteIfBlock: ["expression", "children", "else"],
    SvelteElseBlock: ["children"],
    SvelteEachBlock: [
        "expression",
        "context",
        "index",
        "key",
        "children",
        "else",
    ],
    SvelteAwaitBlock: ["expression", "pending", "then", "catch"],
    SvelteAwaitPendingBlock: ["children"],
    SvelteAwaitThenBlock: ["value", "children"],
    SvelteAwaitCatchBlock: ["error", "children"],
    SvelteKeyBlock: ["expression", "children"],
    SvelteSnippetBlock: ["id", "params", "children"],
    SvelteAttribute: ["key", "value"],
    SvelteShorthandAttribute: ["key", "value"],
    SvelteSpreadAttribute: ["argument"],
    SvelteAttachTag: ["expression"],
    SvelteDirective: ["key", "expression"],
    SvelteStyleDirective: ["key", "value"],
    SvelteSpecialDirective: ["key", "expression"],
    SvelteGenericsDirective: ["key", "params"],
    SvelteDirectiveKey: ["name"],
    SvelteSpecialDirectiveKey: [],
    SvelteText: [],
    SvelteHTMLComment: [],
    SvelteReactiveStatement: ["label", "body"],
    SvelteFunctionBindingsExpression: ["expressions"],
};
export const KEYS = unionWith(svelteKeys);
