!function(t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).Pbf=t()}(function(){return function r(s,n,o){function h(i,t){if(!n[i]){if(!s[i]){var e="function"==typeof require&&require;if(!t&&e)return e(i,!0);if(a)return a(i,!0);throw(t=new Error("Cannot find module '"+i+"'")).code="MODULE_NOT_FOUND",t}e=n[i]={exports:{}},s[i][0].call(e.exports,function(t){return h(s[i][1][t]||t)},e,e.exports,r,s,n,o)}return n[i].exports}for(var a="function"==typeof require&&require,t=0;t<o.length;t++)h(o[t]);return h}({1:[function(t,i,e){i.exports=s;var r=t("ieee754");function s(t){this.buf=ArrayBuffer.isView&&ArrayBuffer.isView(t)?t:new Uint8Array(t||0),this.pos=0,this.type=0,this.length=this.buf.length}s.Varint=0,s.Fixed64=1,s.Bytes=2,s.Fixed32=5;var n=4294967296,o=1/n,l="undefined"==typeof TextDecoder?null:new TextDecoder("utf-8");function h(t){return t.type===s.Bytes?t.readVarint()+t.pos:t.pos+1}function a(t,i,e){return e?4294967296*i+(t>>>0):4294967296*(i>>>0)+(t>>>0)}function u(t,i,e){var r=i<=16383?1:i<=2097151?2:i<=268435455?3:Math.floor(Math.log(i)/(7*Math.LN2));e.realloc(r);for(var s=e.pos-1;t<=s;s--)e.buf[s+r]=e.buf[s]}function f(t,i){for(var e=0;e<t.length;e++)i.writeVarint(t[e])}function d(t,i){for(var e=0;e<t.length;e++)i.writeSVarint(t[e])}function p(t,i){for(var e=0;e<t.length;e++)i.writeFloat(t[e])}function c(t,i){for(var e=0;e<t.length;e++)i.writeDouble(t[e])}function w(t,i){for(var e=0;e<t.length;e++)i.writeBoolean(t[e])}function F(t,i){for(var e=0;e<t.length;e++)i.writeFixed32(t[e])}function b(t,i){for(var e=0;e<t.length;e++)i.writeSFixed32(t[e])}function g(t,i){for(var e=0;e<t.length;e++)i.writeFixed64(t[e])}function x(t,i){for(var e=0;e<t.length;e++)i.writeSFixed64(t[e])}function y(t,i){return(t[i]|t[i+1]<<8|t[i+2]<<16)+16777216*t[i+3]}function v(t,i,e){t[e]=i,t[e+1]=i>>>8,t[e+2]=i>>>16,t[e+3]=i>>>24}function V(t,i){return(t[i]|t[i+1]<<8|t[i+2]<<16)+(t[i+3]<<24)}s.prototype={destroy:function(){this.buf=null},readFields:function(t,i,e){for(e=e||this.length;this.pos<e;){var r=this.readVarint(),s=r>>3,n=this.pos;this.type=7&r,t(s,i,this),this.pos===n&&this.skip(r)}return i},readMessage:function(t,i){return this.readFields(t,i,this.readVarint()+this.pos)},readFixed32:function(){var t=y(this.buf,this.pos);return this.pos+=4,t},readSFixed32:function(){var t=V(this.buf,this.pos);return this.pos+=4,t},readFixed64:function(){var t=y(this.buf,this.pos)+y(this.buf,this.pos+4)*n;return this.pos+=8,t},readSFixed64:function(){var t=y(this.buf,this.pos)+V(this.buf,this.pos+4)*n;return this.pos+=8,t},readFloat:function(){var t=r.read(this.buf,this.pos,!0,23,4);return this.pos+=4,t},readDouble:function(){var t=r.read(this.buf,this.pos,!0,52,8);return this.pos+=8,t},readVarint:function(t){var i=this.buf,e=i[this.pos++],r=127&e;if(e<128)return r;if(r|=(127&(e=i[this.pos++]))<<7,e<128)return r;if(r|=(127&(e=i[this.pos++]))<<14,e<128)return r;if(r|=(127&(e=i[this.pos++]))<<21,e<128)return r;var s,n,e=r|=(15&i[this.pos])<<28,i=t,r=this,t=r.buf;if(n=t[r.pos++],s=(112&n)>>4,n<128)return a(e,s,i);if(n=t[r.pos++],s|=(127&n)<<3,n<128)return a(e,s,i);if(n=t[r.pos++],s|=(127&n)<<10,n<128)return a(e,s,i);if(n=t[r.pos++],s|=(127&n)<<17,n<128)return a(e,s,i);if(n=t[r.pos++],s|=(127&n)<<24,n<128)return a(e,s,i);if(n=t[r.pos++],s|=(1&n)<<31,n<128)return a(e,s,i);throw new Error("Expected varint not more than 10 bytes")},readVarint64:function(){return this.readVarint(!0)},readSVarint:function(){var t=this.readVarint();return t%2==1?(t+1)/-2:t/2},readBoolean:function(){return Boolean(this.readVarint())},readString:function(){var t=this.readVarint()+this.pos,i=this.pos;if(12<=(this.pos=t)-i&&l)return e=this.buf,l.decode(e.subarray(i,t));for(var e,r,s,n,o=this.buf,h=t,a="",u=i;u<h;){var f=o[u],d=null,p=239<f?4:223<f?3:191<f?2:1;if(h<u+p)break;1===p?f<128&&(d=f):2===p?128==(192&(r=o[u+1]))&&(d=(31&f)<<6|63&r)<=127&&(d=null):3===p?(r=o[u+1],s=o[u+2],128==(192&r)&&128==(192&s)&&((d=(15&f)<<12|(63&r)<<6|63&s)<=2047||55296<=d&&d<=57343)&&(d=null)):4===p&&(r=o[u+1],s=o[u+2],n=o[u+3],128==(192&r))&&128==(192&s)&&128==(192&n)&&((d=(15&f)<<18|(63&r)<<12|(63&s)<<6|63&n)<=65535||1114112<=d)&&(d=null),null===d?(d=65533,p=1):65535<d&&(d-=65536,a+=String.fromCharCode(d>>>10&1023|55296),d=56320|1023&d),a+=String.fromCharCode(d),u+=p}return a},readBytes:function(){var t=this.readVarint()+this.pos,i=this.buf.subarray(this.pos,t);return this.pos=t,i},readPackedVarint:function(t,i){if(this.type!==s.Bytes)return t.push(this.readVarint(i));var e=h(this);for(t=t||[];this.pos<e;)t.push(this.readVarint(i));return t},readPackedSVarint:function(t){if(this.type!==s.Bytes)return t.push(this.readSVarint());var i=h(this);for(t=t||[];this.pos<i;)t.push(this.readSVarint());return t},readPackedBoolean:function(t){if(this.type!==s.Bytes)return t.push(this.readBoolean());var i=h(this);for(t=t||[];this.pos<i;)t.push(this.readBoolean());return t},readPackedFloat:function(t){if(this.type!==s.Bytes)return t.push(this.readFloat());var i=h(this);for(t=t||[];this.pos<i;)t.push(this.readFloat());return t},readPackedDouble:function(t){if(this.type!==s.Bytes)return t.push(this.readDouble());var i=h(this);for(t=t||[];this.pos<i;)t.push(this.readDouble());return t},readPackedFixed32:function(t){if(this.type!==s.Bytes)return t.push(this.readFixed32());var i=h(this);for(t=t||[];this.pos<i;)t.push(this.readFixed32());return t},readPackedSFixed32:function(t){if(this.type!==s.Bytes)return t.push(this.readSFixed32());var i=h(this);for(t=t||[];this.pos<i;)t.push(this.readSFixed32());return t},readPackedFixed64:function(t){if(this.type!==s.Bytes)return t.push(this.readFixed64());var i=h(this);for(t=t||[];this.pos<i;)t.push(this.readFixed64());return t},readPackedSFixed64:function(t){if(this.type!==s.Bytes)return t.push(this.readSFixed64());var i=h(this);for(t=t||[];this.pos<i;)t.push(this.readSFixed64());return t},skip:function(t){t&=7;if(t===s.Varint)for(;127<this.buf[this.pos++];);else if(t===s.Bytes)this.pos=this.readVarint()+this.pos;else if(t===s.Fixed32)this.pos+=4;else{if(t!==s.Fixed64)throw new Error("Unimplemented type: "+t);this.pos+=8}},writeTag:function(t,i){this.writeVarint(t<<3|i)},realloc:function(t){for(var i,e=this.length||16;e<this.pos+t;)e*=2;e!==this.length&&((i=new Uint8Array(e)).set(this.buf),this.buf=i,this.length=e)},finish:function(){return this.length=this.pos,this.pos=0,this.buf.subarray(0,this.length)},writeFixed32:function(t){this.realloc(4),v(this.buf,t,this.pos),this.pos+=4},writeSFixed32:function(t){this.realloc(4),v(this.buf,t,this.pos),this.pos+=4},writeFixed64:function(t){this.realloc(8),v(this.buf,-1&t,this.pos),v(this.buf,Math.floor(t*o),this.pos+4),this.pos+=8},writeSFixed64:function(t){this.realloc(8),v(this.buf,-1&t,this.pos),v(this.buf,Math.floor(t*o),this.pos+4),this.pos+=8},writeVarint:function(t){if(268435455<(t=+t||0)||t<0){var i=t,e=this;if(0<=i?(r=i%4294967296|0,s=i/4294967296|0):(s=~(-i/4294967296),4294967295^(r=~(-i%4294967296))?r=r+1|0:s=s+1|(r=0)),0x10000000000000000<=i||i<-0x10000000000000000)throw new Error("Given varint doesn't fit into 10 bytes");e.realloc(10);var i=r,r=e,i=(r.buf[r.pos++]=127&i|128,i>>>=7,r.buf[r.pos++]=127&i|128,i>>>=7,r.buf[r.pos++]=127&i|128,i>>>=7,r.buf[r.pos++]=127&i|128,i>>>=7,r.buf[r.pos]=127&i,s),s=e,e=(7&i)<<4;s.buf[s.pos++]|=e|((i>>>=3)?128:0),i&&(s.buf[s.pos++]=127&i|((i>>>=7)?128:0),i)&&(s.buf[s.pos++]=127&i|((i>>>=7)?128:0),i)&&(s.buf[s.pos++]=127&i|((i>>>=7)?128:0),i)&&(s.buf[s.pos++]=127&i|((i>>>=7)?128:0),i)&&(s.buf[s.pos++]=127&i)}else this.realloc(4),this.buf[this.pos++]=127&t|(127<t?128:0),t<=127||(this.buf[this.pos++]=127&(t>>>=7)|(127<t?128:0),t<=127)||(this.buf[this.pos++]=127&(t>>>=7)|(127<t?128:0),t<=127)||(this.buf[this.pos++]=t>>>7&127)},writeSVarint:function(t){this.writeVarint(t<0?2*-t-1:2*t)},writeBoolean:function(t){this.writeVarint(Boolean(t))},writeString:function(t){t=String(t),this.realloc(4*t.length),this.pos++;var i=this.pos,t=(this.pos=function(t,i,e){for(var r,s,n=0;n<i.length;n++){if(55295<(r=i.charCodeAt(n))&&r<57344){if(!s){56319<r||n+1===i.length?(t[e++]=239,t[e++]=191,t[e++]=189):s=r;continue}if(r<56320){t[e++]=239,t[e++]=191,t[e++]=189,s=r;continue}r=s-55296<<10|r-56320|65536,s=null}else s&&(t[e++]=239,t[e++]=191,t[e++]=189,s=null);r<128?t[e++]=r:(r<2048?t[e++]=r>>6|192:(r<65536?t[e++]=r>>12|224:(t[e++]=r>>18|240,t[e++]=r>>12&63|128),t[e++]=r>>6&63|128),t[e++]=63&r|128)}return e}(this.buf,t,this.pos),this.pos-i);128<=t&&u(i,t,this),this.pos=i-1,this.writeVarint(t),this.pos+=t},writeFloat:function(t){this.realloc(4),r.write(this.buf,t,this.pos,!0,23,4),this.pos+=4},writeDouble:function(t){this.realloc(8),r.write(this.buf,t,this.pos,!0,52,8),this.pos+=8},writeBytes:function(t){var i=t.length;this.writeVarint(i),this.realloc(i);for(var e=0;e<i;e++)this.buf[this.pos++]=t[e]},writeRawMessage:function(t,i){this.pos++;var e=this.pos,t=(t(i,this),this.pos-e);128<=t&&u(e,t,this),this.pos=e-1,this.writeVarint(t),this.pos+=t},writeMessage:function(t,i,e){this.writeTag(t,s.Bytes),this.writeRawMessage(i,e)},writePackedVarint:function(t,i){i.length&&this.writeMessage(t,f,i)},writePackedSVarint:function(t,i){i.length&&this.writeMessage(t,d,i)},writePackedBoolean:function(t,i){i.length&&this.writeMessage(t,w,i)},writePackedFloat:function(t,i){i.length&&this.writeMessage(t,p,i)},writePackedDouble:function(t,i){i.length&&this.writeMessage(t,c,i)},writePackedFixed32:function(t,i){i.length&&this.writeMessage(t,F,i)},writePackedSFixed32:function(t,i){i.length&&this.writeMessage(t,b,i)},writePackedFixed64:function(t,i){i.length&&this.writeMessage(t,g,i)},writePackedSFixed64:function(t,i){i.length&&this.writeMessage(t,x,i)},writeBytesField:function(t,i){this.writeTag(t,s.Bytes),this.writeBytes(i)},writeFixed32Field:function(t,i){this.writeTag(t,s.Fixed32),this.writeFixed32(i)},writeSFixed32Field:function(t,i){this.writeTag(t,s.Fixed32),this.writeSFixed32(i)},writeFixed64Field:function(t,i){this.writeTag(t,s.Fixed64),this.writeFixed64(i)},writeSFixed64Field:function(t,i){this.writeTag(t,s.Fixed64),this.writeSFixed64(i)},writeVarintField:function(t,i){this.writeTag(t,s.Varint),this.writeVarint(i)},writeSVarintField:function(t,i){this.writeTag(t,s.Varint),this.writeSVarint(i)},writeStringField:function(t,i){this.writeTag(t,s.Bytes),this.writeString(i)},writeFloatField:function(t,i){this.writeTag(t,s.Fixed32),this.writeFloat(i)},writeDoubleField:function(t,i){this.writeTag(t,s.Fixed64),this.writeDouble(i)},writeBooleanField:function(t,i){this.writeVarintField(t,Boolean(i))}}},{ieee754:2}],2:[function(t,i,e){e.read=function(t,i,e,r,s){var n,o,h=8*s-r-1,a=(1<<h)-1,u=a>>1,f=-7,d=e?s-1:0,p=e?-1:1,s=t[i+d];for(d+=p,n=s&(1<<-f)-1,s>>=-f,f+=h;0<f;n=256*n+t[i+d],d+=p,f-=8);for(o=n&(1<<-f)-1,n>>=-f,f+=r;0<f;o=256*o+t[i+d],d+=p,f-=8);if(0===n)n=1-u;else{if(n===a)return o?NaN:1/0*(s?-1:1);o+=Math.pow(2,r),n-=u}return(s?-1:1)*o*Math.pow(2,n-r)},e.write=function(t,i,e,r,s,n){var o,h,a=8*n-s-1,u=(1<<a)-1,f=u>>1,d=23===s?Math.pow(2,-24)-Math.pow(2,-77):0,p=r?0:n-1,l=r?1:-1,n=i<0||0===i&&1/i<0?1:0;for(i=Math.abs(i),isNaN(i)||i===1/0?(h=isNaN(i)?1:0,o=u):(o=Math.floor(Math.log(i)/Math.LN2),i*(r=Math.pow(2,-o))<1&&(o--,r*=2),2<=(i+=1<=o+f?d/r:d*Math.pow(2,1-f))*r&&(o++,r/=2),u<=o+f?(h=0,o=u):1<=o+f?(h=(i*r-1)*Math.pow(2,s),o+=f):(h=i*Math.pow(2,f-1)*Math.pow(2,s),o=0));8<=s;t[e+p]=255&h,p+=l,h/=256,s-=8);for(o=o<<s|h,a+=s;0<a;t[e+p]=255&o,p+=l,o/=256,a-=8);t[e+p-l]|=128*n}},{}]},{},[1])(1)});
