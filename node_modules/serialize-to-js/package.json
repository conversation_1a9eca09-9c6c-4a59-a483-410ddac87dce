{"name": "serialize-to-js", "version": "3.1.2", "description": "serialize objects to javascript", "keywords": ["javascript", "objects", "serialize"], "homepage": "https://github.com/commenthol/serialize-to-js", "bugs": {"url": "https://github.com/commenthol/serialize-to-js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/commenthol/serialize-to-js.git"}, "license": "MIT", "author": "commenthol <<EMAIL>>", "maintainers": "commenthol <<EMAIL>>", "main": "lib", "module": "src", "directories": {"lib": "lib", "test": "test"}, "files": ["src", "lib"], "scripts": {"build": "babel -d lib src", "ci": "npm run clean && npm run lint && npm run build &&  npm test", "clean": "rimraf lib doc coverage .nyc_output *.tgz", "coverage": "nyc -r text -r html npm test", "lint": "eslint src test", "prepublishOnly": "npm run ci", "readme": "markedpp --githubid -i README.md -o README.md", "test": "mocha"}, "babel": {"presets": ["@babel/preset-env"]}, "eslintConfig": {"env": {"mocha": true}, "plugins": ["standard"], "extends": "standard", "rules": {"key-spacing": 0, "no-console": 1}}, "mocha": {"check-leaks": true}, "dependencies": {}, "devDependencies": {"@babel/cli": "^7.17.6", "@babel/core": "^7.17.8", "@babel/preset-env": "^7.16.11", "eslint": "^7.32.0", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.25.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.3.1", "eslint-plugin-standard": "^4.1.0", "mocha": "^9.2.2", "nyc": "^15.1.0", "rimraf": "^3.0.2"}, "engines": {"node": ">=4.0.0"}}